#include <TcmTypes.h>
#include <ctype.h>
#include <errno.h>
//#include <linux/unaligned/be_byteshift.h>
#include <machine/va-loongarch.h>
//#include <pmon.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/arch/loongarch/include/stdarg.h>
#include <linux/types.h>
#include <tcm-common.h>
#include <tcm-v2.h>
#include "tcm_unaligned.h"
#include "tcm-utils.h"
#include "tcm2_mailbox.h"
#include "tcm2_log.h"
#include "tcm2_tis.h"

#define DIV_ROUND_UP(n, d) (((n) + (d) - 1) / (d))

void *parse_byte_string(char *bytes, u8 *data, u32 *count_ptr) {
    char byte[3];
    u32 count, length;
    int i;
    static u8 static_buffer[1024];

    length = strlen(bytes);
    count = length / 2;

    if(!data) {
        if(count > sizeof(static_buffer)) {
            return NULL;
        }
        data = static_buffer;
    }
    if(!data)
        return NULL;

    byte[2] = '\0';
    for(i = 0; i < length; i += 2) {
        byte[0] = bytes[i];
        byte[1] = bytes[i + 1];
        data[i / 2] = (u8)strtoul(byte, NULL, 16);
    }

    if(count_ptr)
        *count_ptr = count;

    return data;
}

int pack_byte_string(u8 *str, u32 size, const char *format, ...) {
    va_list args;
    u32 offset = 0, length = 0;
    u8 *data = NULL;
    u32 value = 0;

    va_start(args, format);
    for(; *format; format++) {
        switch(*format) {
            case 'b':
                offset = va_arg(args, u32);
                value = va_arg(args, int);
                length = 1;
                break;
            case 'w':
                offset = va_arg(args, u32);
                value = va_arg(args, int);
                length = 2;
                break;
            case 'd':
                offset = va_arg(args, u32);
                value = va_arg(args, u32);
                length = 4;
                break;
            case 's':
                offset = va_arg(args, u32);
                data = va_arg(args, u8 *);
                length = va_arg(args, u32);
                break;
            default:
                printf("Couldn't recognize format string\n");
                va_end(args);
                return -1;
        }

        if(offset + length > size) {
            va_end(args);
            return -1;
        }

        switch(*format) {
            case 'b':
                str[offset] = value;
                break;
            case 'w':
                put_unaligned_be16(value, str + offset);
                break;
            case 'd':
                put_unaligned_be32(value, str + offset);
                break;
            case 's':
                memcpy(str + offset, data, length);
                break;
        }
    }
    va_end(args);

    return 0;
}

int unpack_byte_string(const u8 *str, size_t size, const char *format, ...) {
    va_list args;
    size_t offset = 0, length = 0;
    u8 *ptr8 = NULL;
    u16 *ptr16 = NULL;
    u32 *ptr32 = NULL;

    va_start(args, format);
    for(; *format; format++) {
        switch(*format) {
            case 'b':
                offset = va_arg(args, size_t);
                ptr8 = va_arg(args, u8 *);
                length = 1;
                if(size < offset + length) {
                    tcm_err("%s:%d Failed to read: size=%zd, offset=%zx, len=%zx", __func__, __LINE__, size, offset, length);
                    return -1;
                }
                *ptr8 = str[offset];
                break;
            case 'w':
                offset = va_arg(args, size_t);
                ptr16 = va_arg(args, u16 *);
                length = 2;
                if(size < offset + length) {
                    tcm_err("%s:%d Failed to read: size=%zd, offset=%zx, len=%zx", __func__, __LINE__, size, offset, length);
                    return -1;
                }
                *ptr16 = __get_unaligned_be16(str + offset);
                break;
            case 'd':
                offset = va_arg(args, size_t);
                ptr32 = va_arg(args, u32 *);
                length = 4;
                if(size < offset + length) {
                    tcm_err("%s:%d Failed to read: size=%zd, offset=%zx, len=%zx", __func__, __LINE__, size, offset, length);
                    return -1;
                }
                *ptr32 = get_unaligned_be32(str + offset);
                break;
            case 's':
                offset = va_arg(args, size_t);
                ptr8 = va_arg(args, u8 *);
                length = va_arg(args, u32);

                // If the previous parameter is 'w', adjust length
                if(*(format - 1) == 'w') {
                    length = *((u16 *)ptr16);  // Use the previously parsed `u16` as length
                }

                if(size < offset + length) {
                    tcm_err("%s:%d Failed to read: size=%zd, offset=%zx, len=%zx", __func__, __LINE__, size, offset, length);
                    return -1;
                }
                memcpy(ptr8, str + offset, length);
                break;
            default:
                va_end(args);
                tcm_debug("Couldn't recognize format string\n");
                return -1;
        }
    }
    va_end(args);

    return 0;
}

u32 tcm_command_size(const void *command) {
    const u32 command_size_offset = 2;

    return get_unaligned_be32(command + command_size_offset);
}

u32 tcm_return_code(const void *response) {
    const u32 return_code_offset = 6;

    return get_unaligned_be32(response + return_code_offset);
}

u32 tcm_sendrecv_command(const void *command,
                         void *response, u32 *size_ptr) {
    int err, ret;
    u8 response_buffer[COMMAND_BUFFER_SIZE];
    u32 response_length;
    uint size;

    if(response) {
        response_length = *size_ptr;
    } else {
        response = response_buffer;
        response_length = sizeof(response_buffer);
    }

    size = tcm_command_size(command);

    if(size > COMMAND_BUFFER_SIZE) {
        tcm_err("size returning err =%d\n", -E2BIG);
        return -E2BIG;
    }

    if(debug_data) {
    printf("Send Data[size:%zu]: \n", size);
    for(size_t i = 0; i < size; i++) {
        printf("%02x", ((uint8_t *)command)[i]);
        if((i + 1) % 2 == 0)
            printf(" ");
        if((i + 1) % 16 == 0)
            printf("\n");
        else if(i == size - 1)
            printf("\n");
    }
    printf("\n");
    }

    err = tcm_xfer(command, size, response, &response_length);

    if(debug_data) {
    printf("Recv Data[size:%zu]: \n", response_length);
    for(size_t i = 0; i < response_length; i++) {
        printf("%02x", ((uint8_t *)response)[i]);
        if((i + 1) % 2 == 0)
            printf(" ");
        if((i + 1) % 16 == 0)
            printf("\n");
        else if(i == response_length - 1)
            printf("\n");
    }
    printf("\n");
    }


    if(err < 0)
        return err;

    if(size_ptr)
        *size_ptr = response_length;

    ret = tcm_return_code(response);
    return ret;
}

u32 tcm2_startup(enum tcm2_startup_types mode) {
    const u8 command_v2[12] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(12),
        tcm_u32(TCM2_CC_Startup),
        tcm_u16(mode),
    };
    int ret;

    /*
     * TCM2_Startup command returns RC_SUCCESS the first time,
     * but will return RC_INITIALIZE afterwards.
     */
    ret = tcm_sendrecv_command(command_v2, NULL, NULL);
    if(ret && ret != TCM2_RC_INITIALIZE)
        return ret;

    return 0;
}

u32 tcm2_self_test(enum tcm2_yes_no full_test) {
    const u8 command_v2[12] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(11),
        tcm_u32(TCM2_CC_SelfTest),
        full_test,
    };
    return tcm_sendrecv_command(command_v2, NULL, NULL);
}

int tcm2_get_random(void *data, u32 count) {
    const u8 command_v2[10] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(12),
        tcm_u32(TCM2_CC_GetRandom),
    };
    u8 buf[COMMAND_BUFFER_SIZE], response[COMMAND_BUFFER_SIZE];

    const size_t data_size_offset = 10;
    const size_t data_offset = 12;
    size_t response_length = sizeof(response);
    u16 data_size;
    u8 *out = data;
    while(count > 0) {
        u32 this_bytes = min(count, sizeof(response) - data_offset);
        u32 err;

        if(pack_byte_string(buf, sizeof(buf), "sw", 0, command_v2, sizeof(command_v2), sizeof(command_v2), this_bytes))
            return -1;
        err = tcm_sendrecv_command(buf, response, &response_length);
        if(err)
            return -2;
        if(unpack_byte_string(response, response_length, "ws", data_size_offset, &data_size, data_offset, out, data_size))
            return -3;
        /* if(unpack_byte_string(response, response_length, "s", data_offset, out, data_size))
            return -4; */
        count -= data_size;
        out += data_size;
    }

    /*     u8 buf[COMMAND_BUFFER_SIZE] = {
            0x80, 0x01, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x01, 0x7b, 0x00, 0x01};
        u8 response[COMMAND_BUFFER_SIZE];
        size_t response_length = sizeof(response);
        u32 err = tcm_sendrecv_command(buf, response, &response_length);
        if(err)
            return err;
        return 0; */
    return 0;
}

int tcm2_getrandom_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <count> [addr]\n", argv[0]);
        printf("Generate random bytes using the TCM\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <count>        : Number of random bytes to generate\n");
        printf("  [addr]         : Optional - Memory address to store the random data\n");
        printf("                   If not provided, data will be stored in an internal buffer\n");
        printf("Examples:\n");
        printf("  %s 16          : Generate 16 random bytes and display them\n", argv[0]);
        printf("  %s 32 0x80000000 : Generate 32 random bytes and store at address 0x80000000\n", argv[0]);
        return 0;
    }
    
    if(argc < 2 || argc > 3) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -EINVAL;
    }
    static u8 internal_buffer[4096];
    u8 *data = internal_buffer;
    u32 max_count = sizeof(internal_buffer);
    char *endptr;
    
    u32 count = strtoul(argv[1], &endptr, 0);
    if(*endptr != '\0') {
        tcm_err("Invalid count value\n");
        return -EINVAL;
    }

    if(argc == 3) {
        ulong addr = strtoul(argv[2], &endptr, 0);
        if(*endptr != '\0' || addr == 0) {
            tcm_err("Invalid address value, using internal buffer\n");
        } else {
            data = (u8 *)addr;
            max_count = 0xFFFFFFFF;
        }
    }

    if(count > max_count) {
        tcm_err("Count exceeds internal buffer size(%zu), truncating\n", sizeof(internal_buffer));
        count = max_count;
    }

    int ret = tcm2_get_random(data, count);
    if(ret != 0) {
        tcm_err("Failed to get random data, error: %d\n", ret);
        return ret;
    }

    if(data == internal_buffer) {
        tcm_print_data("Random data", data, count);
    } else {
        tcm_debug("Data stored at: 0x%lx\n", (ulong)data);
    }
    return 0;
}

u32 tcm2_clear(u32 handle, const char *pw, const ssize_t pw_sz) {
    uint offset = 27;
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS), /* TAG */
        tcm_u32(offset + pw_sz),   /* length */
        tcm_u32(TCM2_CC_Clear),    /* command code */

        /* HANDLE */
        tcm_u32(handle), /* TCM resource handle */

        /* AUTH_SESSION */
        tcm_u32(9 + pw_sz),  /* authorization size */
        tcm_u32(TCM2_RS_PW), /* session handle */
        tcm_u16(0),          /* <nonce> size */
                             /* <nonce> (if any) */
        0,                   /* attributes: Cont/Excl/Rst */
        tcm_u16(pw_sz),      /* <hmac/password> size */
                             /* <hmac/password> (if any) */
    };
    int ret;

    /*
     * Fill command structure starting from the first buffer:
     *     - password (if any)
     */
    ret = pack_byte_string(command_v2, sizeof(command_v2), "s",
                           offset, pw, pw_sz);
    offset += pw_sz;
    if(ret)
        return TCM_LIB_ERROR;

    return tcm_sendrecv_command(command_v2, NULL, NULL);
}

u32 tcm2_get_capability(u32 capability, u32 property,
                        void *buf, u32 count) {
    uint8_t command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),   /* TAG */
        tcm_u32(22),                    /* length */
        tcm_u32(TCM2_CC_GetCapability), /* command code */

        tcm_u32(capability), /* capability */
        tcm_u32(property),   /* property */
        tcm_u32(count),      /* property count */
    };
    uint8_t response[COMMAND_BUFFER_SIZE];
    uint32_t response_len = COMMAND_BUFFER_SIZE;
    uint32_t properties_off;
    uint32_t actual_count;
    int ret;

    ret = tcm_sendrecv_command(command_v2, response, &response_len);
    if(ret)
        return ret;

    /*
     * In the response buffer, properties are located after the following fields:
     * tag (u16), response size (u32), response code (u32),
     * YES/NO flag (u8), TCM_CAP (u32), property count (u32)
     */
    properties_off = sizeof(uint16_t) + sizeof(uint32_t) + sizeof(uint32_t) +
                     sizeof(uint8_t) + sizeof(uint32_t) + sizeof(uint32_t);

    // Get the actual number of returned properties
    memcpy(&actual_count, &response[properties_off - sizeof(uint32_t)], sizeof(uint32_t));

    // Only copy the actual returned data, not exceeding the requested count
    uint32_t copy_count = (actual_count < count) ? actual_count : count;
    memcpy(buf, &response[properties_off], copy_count * sizeof(uint32_t));

    return 0;
}

u32 tcm2_dam_reset(const char *pw, const ssize_t pw_sz) {
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),                  /* TAG */
        tcm_u32(27 + pw_sz),                        /* length */
        tcm_u32(TCM2_CC_DictionaryAttackLockReset), /* command code */

        /* HANDLE */
        tcm_u32(TCM2_RH_LOCKOUT), /* TCM resource handle */

        /* AUTH_SESSION */
        tcm_u32(9 + pw_sz),  /* authorization size */
        tcm_u32(TCM2_RS_PW), /* session handle */
        tcm_u16(0),          /* <nonce> size */
                             /* <nonce> (if any) */
        0,                   /* attributes: Cont/Excl/Rst */
        tcm_u16(pw_sz),      /* <hmac/password> size */
                             /* <hmac/password> (if any) */
    };
    u32 offset = 27;
    int ret;

    /*
     * Fill command structure starting from the first buffer:
     *     - password (if any)
     */
    ret = pack_byte_string(command_v2, sizeof(command_v2), "s",
                           offset, pw, pw_sz);
    offset += pw_sz;
    if(ret)
        return TCM_LIB_ERROR;

    return tcm_sendrecv_command(command_v2, NULL, NULL);
}

u32 tcm2_dam_parameters(const char *pw, const ssize_t pw_sz, u32 max_tries,
                        u32 recovery_time, u32 lockout_recovery) {
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),                   /* TAG */
        tcm_u32(27 + pw_sz + 12),                    /* length */
        tcm_u32(TCM2_CC_DictionaryAttackParameters), /* command code */

        /* HANDLE */
        tcm_u32(TCM2_RH_LOCKOUT), /* TCM resource handle */

        /* AUTH_SESSION */
        tcm_u32(9 + pw_sz),  /* authorization size */
        tcm_u32(TCM2_RS_PW), /* session handle */
        tcm_u16(0),          /* <nonce> size */
                             /* <nonce> (if any) */
        0,                   /* attributes: Cont/Excl/Rst */
        tcm_u16(pw_sz),      /* <hmac/password> size */
                             /* <hmac/password> (if any) */
    };
    u32 offset = 27;
    int ret;

    printf("Packing command_v2 buffer with DAM parameters:\n");
    printf("- maxTries: %u\n", max_tries);
    printf("- recoveryTime: %u\n", recovery_time);
    printf("- lockoutRecovery: %u\n", lockout_recovery);
    printf("- password size: %zd\n", pw_sz);

    ret = pack_byte_string(command_v2, sizeof(command_v2), "sddd",
                           offset, pw, pw_sz,
                           offset + pw_sz, max_tries,
                           offset + pw_sz + 4, recovery_time,
                           offset + pw_sz + 8, lockout_recovery);
    offset += pw_sz + 12;
    if(ret) {
        printf("Error: Failed to pack command_v2 buffer\n");
        return TCM_LIB_ERROR;
    }

    printf("Command buffer contents:\n");
    for(int i = 0; i < offset; ++i) {
        printf("%02X ", command_v2[i]);
        if((i + 1) % 16 == 0 || i == offset - 1)
            printf("\n");
    }

    return tcm_sendrecv_command(command_v2, NULL, NULL);
}

int tcm2_change_auth(u32 handle, const char *newpw,
                     const ssize_t newpw_sz, const char *oldpw,
                     const ssize_t oldpw_sz) {
    u32 offset = 27;
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),                 /* TAG */
        tcm_u32(offset + oldpw_sz + 2 + newpw_sz), /* length */
        tcm_u32(TCM2_CC_HierarchyChangeAuth),      /* command code */

        /* HANDLE */
        tcm_u32(handle), /* TCM resource handle */

        /* AUTH_SESSION */
        tcm_u32(9 + oldpw_sz), /* authorization size */
        tcm_u32(TCM2_RS_PW),   /* session handle */
        tcm_u16(0),            /* <nonce> size */
                               /* <nonce> (if any) */
        0,                     /* attributes: Cont/Excl/Rst */
        tcm_u16(oldpw_sz)      /* <hmac/password> size */
                               /* <hmac/password> (if any) */

        /* TCM2B_AUTH (TCM2B_DIGEST) */
        /* tcm_u16(newpw_sz)             digest size, new password length */
        /* string(newpw)                 digest buffer, new password */
    };
    int ret;

    /*
     * Fill command structure starting from the first buffer:
     *     - old password (if any)
     *     - new password size
     *     - new password
     */
    ret = pack_byte_string(command_v2, sizeof(command_v2), "sws",
                           offset, oldpw, oldpw_sz,
                           offset + oldpw_sz, newpw_sz,
                           offset + oldpw_sz + 2, newpw, newpw_sz);
    offset += oldpw_sz + 2 + newpw_sz;
    if(ret)
        return TCM_LIB_ERROR;

    return tcm_sendrecv_command(command_v2, NULL, NULL);
}

u32 tcm2_write_lock(u32 index) {
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        /* header 10 bytes */
        tcm_u16(TCM2_ST_SESSIONS),     /* TAG */
        tcm_u32(10 + 8 + 13),          /* length */
        tcm_u32(TCM2_CC_NV_WriteLock), /* command code */

        /* handle 8 bytes */
        tcm_u32(TCM2_RH_PLATFORM),    /* main platform seed */
        tcm_u32(HR_NV_INDEX + index), /* password authorization */

        /* session header 9 bytes */
        tcm_u32(9),          /* header size */
        tcm_u32(TCM2_RS_PW), /* password authorization */
        tcm_u16(0),          /* nonce_size */
        0,                   /* session attributes */
        tcm_u16(0),          /* auth_size */
    };

    return tcm_sendrecv_command(command_v2, NULL, NULL);
}

int tcm2_startup_cmd(int argc, char **argv) {
    enum tcm2_startup_types mode;

    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <mode>\n", argv[0]);
        printf("Issue a TCM2_Startup command to initialize the TCM\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <mode>         : Startup mode, one of:\n");
        printf("                   -c - Reset state (TCM2_SU_CLEAR)\n");
        printf("                   -s - Preserved state (TCM2_SU_STATE)\n");
        printf("Examples:\n");
        printf("  %s -c          : Start TCM in clear mode\n", argv[0]);
        printf("  %s -s          : Start TCM in state preservation mode\n", argv[0]);
        return 0;
    }
    
    if(argc != 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(!strcmp("-c", argv[1])) {
        mode = TCM2_SU_CLEAR;
    } else if(!strcmp("-s", argv[1])) {
        mode = TCM2_SU_STATE;
    } else {
        printf("Couldn't recognize mode string: %s\n", argv[1]);
        return -1;
    }

    return tcm2_startup(mode);
}

int tcm2_selftest_cmd(int argc, char **argv) {
    enum tcm2_yes_no full_test;

    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <type>\n", argv[0]);
        printf("Test the TCM capabilities\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <type>         : Test type, one of:\n");
        printf("                   full - Perform all tests\n");
        printf("                   continue - Only check untested tests\n");
        printf("Examples:\n");
        printf("  %s full        : Perform a full self-test of all TCM capabilities\n", argv[0]);
        printf("  %s continue    : Continue testing only untested TCM capabilities\n", argv[0]);
        return 0;
    }
    
    if(argc != 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(!strcasecmp("full", argv[1])) {
        full_test = TCMI_YES;
    } else if(!strcasecmp("continue", argv[1])) {
        full_test = TCMI_NO;
    } else {
        printf("Couldn't recognize test mode: %s\n", argv[1]);
        return -1;
    }

    return tcm2_self_test(full_test);
}

int tcm2_clear_cmd(int argc, char **argv) {
    u32 handle = 0;
    const char *pw = (argc < 3) ? NULL : argv[2];
    const ssize_t pw_sz = pw ? strlen(pw) : 0;

    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <hierarchy> [password]\n", argv[0]);
        printf("Issue a TCM2_Clear command to clear all TCM state\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <hierarchy>    : Hierarchy to use, one of:\n");
        printf("                   TCM2_RH_LOCKOUT - Use lockout hierarchy\n");
        printf("                   TCM2_RH_PLATFORM - Use platform hierarchy\n");
        printf("  [password]     : Optional - Authorization password for the hierarchy\n");
        printf("Examples:\n");
        printf("  %s TCM2_RH_LOCKOUT           : Clear TCM using lockout hierarchy with no password\n", argv[0]);
        printf("  %s TCM2_RH_PLATFORM password : Clear TCM using platform hierarchy with password\n", argv[0]);
        return 0;
    }
    
    if(argc < 2 || argc > 3) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(pw_sz > TCM2_DIGEST_LEN && pw_sz < 10)
        return -EINVAL;

    if(!strcasecmp("TCM2_RH_LOCKOUT", argv[1]))
        handle = TCM2_RH_LOCKOUT;
    else if(!strcasecmp("TCM2_RH_PLATFORM", argv[1]))
        handle = TCM2_RH_PLATFORM;
    else
        return -1;

    return tcm2_clear(handle, pw, pw_sz);
}

int tcm2_getcap_cmd(int argc, char **argv) {
    uint32_t capability, property, rc;
    uint32_t *data;
    uint32_t count;
    uint16_t search_alg_id = 0;
    int i;
    int ret;
    int alg_found = 0;

    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <capability> <property> <addr> <count> [<search_alg_id>]\n", argv[0]);
        printf("Read and display TCM capability information\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <capability>   : Capability category to query\n");
        printf("  <property>     : Starting property to query\n");
        printf("  <addr>         : Memory address to store retrieved data (4 bytes per entry)\n");
        printf("  <count>        : Number of entries to retrieve\n");
        printf("  [search_alg_id]: Optional - Algorithm ID to search for (in hexadecimal, e.g., 0x0044 for ECB)\n");
        printf("Examples:\n");
        printf("  %s 1 0x100 0x80000000 20       : Get algorithm capabilities\n", argv[0]);
        printf("  %s 1 0x100 0x80000000 20 0x0044: Search for algorithm ID 0x0044 (ECB)\n", argv[0]);
        return 0;
    }
    
    if(argc != 5 && argc != 6) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    capability = strtoul(argv[1], NULL, 0);
    property = strtoul(argv[2], NULL, 0);
    data = (uint32_t *)strtoul(argv[3], NULL, 0);
    count = strtoul(argv[4], NULL, 0);

    if(argc == 6) {
        search_alg_id = (uint16_t)strtoul(argv[5], NULL, 0);
    }

    ret = tcm2_get_capability(capability, property, data, count);
    if(ret)
        return ret;

    if(argc == 6) {
        printf("Searching for algorithm ID 0x%04x:\n", search_alg_id);
    } else {
        printf("Listing all supported algorithms:\n");
    }

    for(i = 0; i < count && data[i] != 0; i++) {
        uint16_t alg_id = data[i] & 0xFFFF;
        uint16_t attributes = (data[i] >> 16) & 0xFFFF;

        if(argc == 6) {
            if(alg_id == search_alg_id) {
                printf("Algorithm found!\n");
                printf("Algorithm ID: 0x%04x, Attributes: 0x%04x\n", alg_id, attributes);
                alg_found = 1;
                break;
            }
        } else {
            printf("Algorithm ID: 0x%04x, Attributes: 0x%04x\n", alg_id, attributes);
        }
    }

    if(argc == 6 && !alg_found) {
        tcm_err("Algorithm ID 0x%04x not found in the TCM's capabilities.\n", search_alg_id);
    }

    return ret;
}

int tcm2_damreset_cmd(int argc, char **argv) {
    const char *pw = (argc < 2) ? NULL : argv[1];
    const ssize_t pw_sz = pw ? strlen(pw) : 0;

    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s [password]\n", argv[0]);
        printf("Reset the TCM Dictionary Attack Mechanism (DAM) counter\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  [password]     : Optional - Authorization password for the lockout hierarchy\n");
        printf("Examples:\n");
        printf("  %s             : Reset DAM counter without password\n", argv[0]);
        printf("  %s password    : Reset DAM counter with authorization password\n", argv[0]);
        return 0;
    }
    
    if(argc > 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(pw_sz > TCM2_DIGEST_LEN)
        return -EINVAL;

    return tcm2_dam_reset(pw, pw_sz);
}

int tcm2_damparameters_cmd(int argc, char **argv) {
    const char *pw = (argc < 5) ? NULL : argv[4];
    const ssize_t pw_sz = pw ? strlen(pw) : 0;

    ulong max_tries;
    ulong recovery_time;
    ulong lockout_recovery;
    char *endptr;

    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <maxTries> <recoveryTime> <lockoutRecovery> [password]\n", argv[0]);
        printf("Set the TCM Dictionary Attack Mechanism (DAM) parameters\n");
        printf("Options:\n");
        printf("  --help, -h       : Display this help message\n");
        printf("  <maxTries>       : Maximum number of failures before lockout (0 means always locking)\n");
        printf("  <recoveryTime>   : Time before decrement of the error counter (0 means no lockout)\n");
        printf("  <lockoutRecovery>: Time of a lockout before the next try (0 means a reboot is needed)\n");
        printf("  [password]       : Optional - Authorization password for the lockout hierarchy\n");
        printf("Examples:\n");
        printf("  %s 5 3600 86400         : Set max tries to 5, recovery time to 1 hour, lockout time to 1 day\n", argv[0]);
        printf("  %s 3 7200 86400 password: Same with authorization password\n", argv[0]);
        return 0;
    }
    
    if(argc < 4 || argc > 5) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }
    /*      printf("Changing dictionary attack parameters:\n");
            printf("- maxTries: %lu\n", max_tries);
            printf("- recoveryTime: %lu\n", recovery_time);
            printf("- lockoutRecovery: %lu\n", lockout_recovery);
            printf("- password: %s\n", pw ? pw : "NULL"); */
    if(pw_sz > TCM2_DIGEST_LEN) {
        tcm_err("Password size (%zd) exceeds maximum digest length (%d)\n", pw_sz, TCM2_DIGEST_LEN);
        return -EINVAL;
    }

    max_tries = strtoul(argv[1], &endptr, 0);
    if(*endptr != '\0' || endptr == argv[1]) {
        tcm_err("Failed to parse max_tries from input: %s\n", argv[1]);
        return -1;
    }

    recovery_time = strtoul(argv[2], &endptr, 0);
    if(*endptr != '\0' || endptr == argv[2]) {
        tcm_err("Failed to parse recovery_time from input: %s\n", argv[2]);
        return -1;
    }

    lockout_recovery = strtoul(argv[3], &endptr, 0);
    if(*endptr != '\0' || endptr == argv[3]) {
        tcm_err("Failed to parse lockout_recovery from input: %s\n", argv[3]);
        return -1;
    }

    return tcm2_dam_parameters(pw, pw_sz, max_tries, recovery_time, lockout_recovery);
}

int tcm2_changeauth_cmd(int argc, char **argv) {
    u32 handle;
    const char *newpw = (argc < 3) ? NULL : argv[2];
    const char *oldpw = (argc < 4) ? NULL : argv[3];
    const ssize_t newpw_sz = newpw ? strlen(newpw) : 0;
    const ssize_t oldpw_sz = oldpw ? strlen(oldpw) : 0;

    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <hierarchy> <new_password> [old_password]\n", argv[0]);
        printf("Change the authorization password for a TCM hierarchy\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <hierarchy>    : The hierarchy to change password for, one of:\n");
        printf("                   TCM2_RH_LOCKOUT - Lockout hierarchy\n");
        printf("                   TCM2_RH_ENDORSEMENT - Endorsement hierarchy\n");
        printf("                   TCM2_RH_OWNER - Owner hierarchy\n");
        printf("                   TCM2_RH_PLATFORM - Platform hierarchy\n");
        printf("  <new_password> : New password to set for the hierarchy\n");
        printf("  [old_password] : Optional - Current password for the hierarchy\n");
        printf("Examples:\n");
        printf("  %s TCM2_RH_OWNER newpass        : Change owner hierarchy password to 'newpass'\n", argv[0]);
        printf("  %s TCM2_RH_LOCKOUT newpass oldpass : Change lockout hierarchy password from 'oldpass' to 'newpass'\n", argv[0]);
        return 0;
    }
    
    if(argc < 3 || argc > 4) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(newpw_sz > TCM2_DIGEST_LEN || oldpw_sz > TCM2_DIGEST_LEN)
        return -EINVAL;

    if(!strcasecmp("TCM2_RH_LOCKOUT", argv[1]))
        handle = TCM2_RH_LOCKOUT;
    else if(!strcasecmp("TCM2_RH_ENDORSEMENT", argv[1]))
        handle = TCM2_RH_ENDORSEMENT;
    else if(!strcasecmp("TCM2_RH_OWNER", argv[1]))
        handle = TCM2_RH_OWNER;
    else if(!strcasecmp("TCM2_RH_PLATFORM", argv[1]))
        handle = TCM2_RH_PLATFORM;
    else
        return -1;

    return tcm2_change_auth(handle, newpw, newpw_sz, oldpw, oldpw_sz);
}
