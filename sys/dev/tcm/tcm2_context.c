#include "tcm2_flushcontext.h"
#include "TcmProfile.h"
#include "TcmTypes.h"
#include "tcm-common.h"
#include "tcm-utils.h"
#include "tcm-v2.h"
#include "tcm2_internal.h"

TCM2_RC tcm2_flushcontext(
    FlushContext_In *in  // IN: input parameter list
) {
    printf("TCM2_FlushContext: %08x\n", in->flushHandle);

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),   // TCM_ST_SESSIONS
        tcm_u32(14),                    // command size (base length + data length + 2 bytes hash algorithm)
        tcm_u32(TCM2_CC_FlushContext),  // TCM2_CC_HMAC command
        tcm_u32(in->flushHandle),       // HMAC key handle
    };

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("Failed to flush context. Error code: 0x%08X\n", ret);
        return ret;
    }

    return TCM2_RC_SUCCESS;
}

int tcm2_flushcontext_cmd(int argc, char **argv) {
    u32 handle;
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <handle>\n", argv[0]);
        printf("Flush a context from the TCM\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <handle>       : The handle of the context to flush (e.g., 0x80000000)\n");
        printf("Examples:\n");
        printf("  %s 0x80000000  : Flush the context with handle 0x80000000\n", argv[0]);
        return 0;
    }
    
    if(argc != 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(sscanf(argv[1], "0x%x", &handle) != 1) {
        tcm_err("%s:%d Invalid handle format. Use hexadecimal format (e.g., 0x80000000), code: 0x%08X", __func__, __LINE__, TCM2_RC_VALUE);
        return TCM2_RC_VALUE;
    }

    FlushContext_In in = {
        .flushHandle = handle
    };

    return tcm2_flushcontext(&in);
}