#include <TcmTypes.h>
#include <string.h>
#include <tcm-common.h>
#include <tcm-v2.h>

#include "tcm-utils.h"
#include "tcm2_internal.h"
#include "tcm2_mailbox.h"

u32 tcm2_encryptDecrypt2(u32 handle, TCMI_YES_NO encrypt, TCMI_ALG_CIPHER_MODE mode, u8 *data, u16 data_len, u8 odata[32], u16 *odata_len) {
    uint16_t pw_sz = 0;
    u8 iv[16] = {
        0x01, 0x02, 0x03, 0x04,
        0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C,
        0x0D, 0x0E, 0x0F, 0x10};

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(27 + pw_sz + 2 + data_len + 21),
        tcm_u32(TCM2_CC_EncryptDecrypt2),
        tcm_u32(handle),

        tcm_u32(9 + pw_sz),
        tcm_u32(TCM2_RS_PW),
        tcm_u16(0),
        0,
        tcm_u16(pw_sz),
    };

    u32 offset = 27;
    tcm_write_u16(command + offset, data_len);
    offset += 2;
    memcpy(command + offset, data, data_len);
    offset += data_len;

    command[offset] = encrypt;
    offset += 1;

    tcm_write_u16(command + offset, mode);
    offset += 2;

    if(mode != TCM2_ALG_ECB) {
        tcm_write_u16(command + offset, 16);
        memcpy(command + offset + 2, iv, 16);
    } else {
        tcm_write_u16(command + offset, 0);
        tcm_write_u32(command + 2, 27 + pw_sz + 2 + data_len + 5);
    }

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("%s:%d Failed to execute SYM, error code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u32 resp_offset = 14;
    *odata_len = tcm_read_u16(response + resp_offset);
    tcm_debug("sym_size: %u\n", *odata_len);
    memcpy(odata, response + resp_offset + 2, *odata_len);
    return 0;
}

u8 ciphertext[1024] = {0};
u16 ciphertext_len = sizeof(ciphertext);

int tcm2_encryptdecrypt2_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);

    // Default to both encrypt and decrypt
    int mode = 0; // 0: both encrypt and decrypt, 1: encrypt only, 2: decrypt only
    
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-enc") == 0 || strcmp(argv[i], "--encrypt") == 0) {
            mode = 1;
            for (int j = i; j < argc - 1; j++)
                argv[j] = argv[j + 1];
            argc--;
            i--;
        } else if (strcmp(argv[i], "-dec") == 0 || strcmp(argv[i], "--decrypt") == 0) {
            mode = 2;
            for (int j = i; j < argc - 1; j++)
                argv[j] = argv[j + 1];
            argc--;
            i--;
        }
    }

    if (argc < 2) {
        tcm_err("Usage: %s [-enc|-dec] <handle> [input_data|address length]", argv[0]);
        return -1;
    }

    u32 handle;
    if(sscanf(argv[1], "0x%x", &handle) != 1) {
        tcm_err("%s:%d Invalid handle format. Use hexadecimal format (e.g., 0x80000000), code: 0x%08X", __func__, __LINE__, TCM2_RC_VALUE);
        return -1;
    }

    u8 *input_data = NULL;
    u16 input_len = 0;
    u8 buffer[32] = {0};
    static u8 ciphertext[32] = {0};
    static u16 ciphertext_len = 0;

    if (argc == 2) {
        if (mode == 2 && ciphertext_len > 0) {
            // No need to set input_data here, as decryption will use the previous ciphertext
        } else {
            // Use default test data
            const char *test_msg = "Test encrypt/decrypt";
            input_len = strlen(test_msg);
            memcpy(buffer, test_msg, input_len);
            input_data = buffer;
        }
    } else if (argc == 3) {
        const char *msg = argv[2];
        input_len = strlen(msg);
        memcpy(buffer, msg, input_len > sizeof(buffer) ? sizeof(buffer) : input_len);
        input_data = buffer;
    } else if (argc == 4) {
        unsigned long long addr = strtoull(argv[2], NULL, 0);
        input_len = strtoul(argv[3], NULL, 0);
        if (input_len > sizeof(buffer)) {
            tcm_err("Input data too large, maximum size is %lu bytes", sizeof(buffer));
            return -1;
        }
        memcpy(buffer, (void *)addr, input_len);
        input_data = buffer;
    } else {
        tcm_err("Usage: %s [-enc|-dec] <handle> [input_data|address length]", argv[0]);
        return -1;
    }

    TCMI_YES_NO encrypt_flag = 0x01; // 1 means encrypt
    TCMI_ALG_CIPHER_MODE cipher_mode = TCM2_ALG_CFB;
    u16 processed_data_len = 32;
    u8 processed_data[32] = {0};
    u32 tcm_result = 0;

    if (mode == 0 || mode == 1) {
        tcm_result = tcm2_encryptDecrypt2(handle, encrypt_flag, cipher_mode, input_data, input_len, processed_data, &processed_data_len);
        if (tcm_result != TCM2_RC_SUCCESS) {
            tcm_err("tcm2_encryptdecrypt failed during encryption, error code: 0x%08X", tcm_result);
            return tcm_result;
        }

        memcpy(ciphertext, processed_data, processed_data_len);
        ciphertext_len = processed_data_len;

        tcm_print_data("[DEBUG] Original data", input_data, input_len);
        tcm_print_data("[DEBUG] Encrypted data", processed_data, processed_data_len);

        if (mode == 1)
            return 0;
    }

    if (mode == 0 || mode == 2) {
        encrypt_flag = 0x00;

        u8 *decrypt_data = mode == 0 ? processed_data : ciphertext;
        u16 decrypt_len = mode == 0 ? processed_data_len : ciphertext_len;

        if (mode == 2)
            tcm_print_data("[DEBUG] Encrypted data", decrypt_data, decrypt_len);

        memset(processed_data, 0, sizeof(processed_data));
        processed_data_len = sizeof(processed_data);
        tcm_result = tcm2_encryptDecrypt2(handle, encrypt_flag, cipher_mode, decrypt_data, decrypt_len, processed_data, &processed_data_len);
        if (tcm_result != TCM2_RC_SUCCESS) {
            tcm_err("tcm2_encryptdecrypt failed during decryption, error code: 0x%08X", tcm_result);
            return tcm_result;
        }

        tcm_print_data("[DEBUG] Decrypted data", processed_data, processed_data_len);
    }

    return 0;
}
