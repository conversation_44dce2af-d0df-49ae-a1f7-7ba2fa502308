# PMON-TCM2.0 命令参考手册

本文档详细介绍了PMON-TCM2.0模块支持的所有命令，包括其用途、参数说明和使用示例。

## 目录

- [编译配置宏](#编译配置宏)
- [初始化说明](#初始化说明)
- [基础命令](#基础命令)
- [PCR相关命令](#pcr相关命令)
- [密钥管理命令](#密钥管理命令)
- [加密解密命令](#加密解密命令)
- [字典攻击防护命令](#字典攻击防护命令)
- [授权管理命令](#授权管理命令)
- [随机数生成命令](#随机数生成命令)
- [签名验证命令](#签名验证命令)
- [会话管理命令](#会话管理命令)
- [哈希运算命令](#哈希运算命令)
- [HMAC运算命令](#hmac运算命令)
- [NV存储命令](#nv存储命令)
- [测试命令](#测试命令)
- [文件系统测试命令](#文件系统测试命令)
- [环境变量度量命令](#环境变量度量命令)
- [安全度量功能](#安全度量功能)
- [故障排除](#故障排除)
- [关键功能总结](#关键功能总结)

## 编译配置宏

TCM2.0 功能通过以下编译宏进行控制：

### 基础宏定义

- **`TCM2`**：启用 TCM2.0 功能支持
  - 定义此宏后，PMON 启动时会自动调用 `tcm2_autostart()` 初始化 TCM 设备
  - 位置：`pmon/common/main.c`

- **`LS2_SE`**：启用龙芯安全增强功能
  - 与 `TCM2` 宏配合使用，提供文件完整性度量功能
  - 在 `load` 和 `boot` 命令中自动进行文件哈希计算和验证

- **`TCM_ENV_MEASUREMENT`**：启用环境变量度量功能
  - 在 PMON 启动时自动度量环境变量并与基线比较
  - 位置：`pmon/common/main.c`

### 安全策略宏

- **`MEASURE_CRITICAL_FILES`**：关键文件度量策略控制
  - 值为 1：度量关键文件列表
  - 值为 0：度量整个文件系统镜像
  - 默认值：0（定义在 `sys/dev/tcm/tcm2_metric.h`）

- **`STOP_ON_MEASURE_FAILURE`**：度量失败处理策略
  - 值为 1：度量失败时停止启动
  - 值为 0：度量失败时继续启动（仅记录警告）
  - 默认值：0（定义在 `sys/dev/tcm/tcm2_metric.h`）

### PMON 配置系统说明

PMON 使用配置文件控制功能模块的编译和启用：

#### 配置文件位置
- 主配置文件：`Targets/soc/conf/<板型名称>`
- 文件编译规则：`Targets/soc/conf/files.<板型名称>`

#### 配置指令说明

**`select cmd_tcm2`**：
- 作用：选择编译 TCM2 相关的所有源文件
- 效果：所有标记为 `cmd_tcm2` 的文件会被编译到 PMON 中
- 结果：TCM 相关命令在 PMON 中可用

**`option TCM2`**：
- 作用：定义 TCM2 宏，启用自动初始化功能
- 效果：在 `main.c` 中自动调用 `tcm2_autostart()`
- 结果：PMON 启动时自动初始化 TCM 设备

**`option LS2_SE`**：
- 作用：启用龙芯安全增强功能
- 效果：在 `load` 和 `boot` 命令中启用文件完整性度量
- 结果：自动计算和验证文件哈希值

**`option TCM_ENV_MEASUREMENT`**：
- 作用：启用环境变量度量功能
- 效果：在 PMON 启动时自动度量环境变量
- 结果：环境变量完整性自动检查


## 初始化说明

### 自动初始化（推荐）

当编译时定义了 `TCM2` 宏，PMON 启动时会自动执行以下初始化序列：

1. **自动 TCM 初始化**：在 `main.c` 中自动调用 `tcm2_autostart()`
2. **自动环境变量度量**：如果定义了 `TCM_ENV_MEASUREMENT` 宏，自动执行环境变量度量
3. **文件完整性度量**：在 `load` 和 `boot` 命令执行时自动进行文件哈希验证

### 手动初始化

根据不同的配置，初始化方式有所不同：

#### 场景1：完整配置（select cmd_tcm2 + option TCM2）
- **命令可用性**：所有 TCM 相关命令均可用
- **初始化方式**：系统自动初始化，无需手动操作
- **配置示例**：
  ```
  select      cmd_tcm2
  option      TCM2
  ```
- **备用方案**：如需重新初始化，可手动执行 `tcm2_autostart`

#### 场景2：手动配置（仅 select cmd_tcm2）
- **命令可用性**：TCM 命令可用，但需要手动初始化
- **配置示例**：
  ```
  select      cmd_tcm2
  #option     TCM2        # 注释掉，不启用自动初始化
  ```
- **初始化步骤**：

1. 执行`tcm_init`初始化芯片

2. 执行 `tcm2_startup` 命令初始化TCM2.0设备

3. (可选)执行 `tcm2_selftest` 命令进行设备自检

4. (可选)如果需要恢复出厂设置，可以执行 `tcm2_clear` 命令

#### 场景3：无 TCM 支持（未 select cmd_tcm2）
- **命令可用性**：**所有 TCM 相关命令均不可用**
- **配置示例**：
  ```
  #select     cmd_tcm2    # 注释掉，不编译 TCM 功能
  #option     TCM2
  ```
- **现象**：在 PMON 命令行中输入 TCM 命令会提示"Unknown command"
- **解决方案**：需要修改对应板型的配置文件 `Targets/soc/conf/<板型名称>`，取消注释 `select cmd_tcm2` 并重新编译

#### 通用操作说明

5. 所有地址操作需要先使用命令：

   ```shell
   PMON> test_malloc 32   #创建32字节的空间，并且会返回一个地址addr
   #d8 addr 4 以8字节读取，共读取4次
   #m8 addr data 以8字节写入data数据至addr
   ```

6. 所有命令中加上`-d`参数可以查看日志等相关调试输出

7. 算法仅支持SM2、SM3、SM4

## 基础命令

### tcm2_autostart

**功能描述**：自动初始化TCM设备并启动。该命令会依次执行 `tcm_init` 和 `tcm2_startup -c`，提供一键式初始化功能。

**用法**：

```shell
tcm2_autostart
```

**示例**：

```shell
PMON> tcm2_autostart    # 自动初始化并启动TCM设备
```

**注意事项**：

- 如果TCM设备已经初始化，命令会显示相应信息并直接返回
- 该命令使用清除模式启动TCM设备
- 初始化失败时会显示详细的错误信息

### tcm_remove

**功能描述**：释放TCM设备资源并关闭设备。该命令用于清理TCM设备的占用状态。

**用法**：

```shell
tcm_remove
```

**示例**：

```shell
PMON> tcm_remove    # 释放TCM设备资源
```

**注意事项**：

- 执行此命令后，需要重新执行 `tcm_init` 才能使用TCM功能
- 建议在不再使用TCM功能时执行此命令以释放资源

### tcm2_startup

**功能描述**：初始化TCM2.0设备，在使用其他TCM命令之前必须首先执行此命令。该命令用于将TCM从关闭状态转换为操作状态。

**用法**：

```shell
tcm2_startup <mode>
```

**参数说明**：

- mode：启动模式
  - TCM2_SU_CLEAR(-c)：重置状态（清除所有易失性数据）
  - TCM2_SU_STATE(-s)：保持状态（保留之前的状态）

**示例**：

```shell
PMON> tcm2_startup -c    # 使用清除模式启动
PMON> tcm2_startup -s    # 使用状态保持模式启动
```

### tcm2_selftest

**功能描述**：执行TCM2.0设备的自检操作，验证设备功能是否正常。可以选择执行完整测试或仅测试未测试的功能。

**用法**：

```shell
tcm2_selftest <type>
```

**参数说明**：

- type：测试类型
  - full：执行所有测试
  - continue：仅检查未测试的功能

**示例**：

```shell
PMON> tcm2_selftest full      # 执行完整测试
PMON> tcm2_selftest continue  # 执行未完成的测试
```

### tcm2_clear

**功能描述**：清除TCM2.0设备中的所有数据和密钥，将设备恢复到出厂状态。此操作需要相应的授权。

**用法**：

```shell
tcm2_clear <hierarchy> [auth]
```

**参数说明**：

- hierarchy：权限层级
  - TCM2_RH_LOCKOUT：锁定授权
  - TCM2_RH_PLATFORM：平台授权
- auth：可选的授权密码（如果设置了授权值）
  - 密码长度必须大于TCM2_DIGEST_LEN且小于10字节

**示例**：

```shell
PMON> tcm2_clear TCM2_RH_LOCKOUT           # 使用锁定授权清除
PMON> tcm2_clear TCM2_RH_PLATFORM password  # 使用带密码的平台授权清除
```

## PCR相关命令

### tcm2_pcrreset

**功能描述**：重置指定的PCR寄存器到初始状态。

**用法**：

```shell
tcm2_pcrreset <pcr>
```

**参数说明**：

- pcr：PCR寄存器索引（0-23）

**示例**：

```shell
PMON> tcm2_pcrreset 16  # 重置PCR 16到初始状态
```

### tcm2_pcrread

**功能描述**：读取指定PCR寄存器的当前值。

**用法**：

```shell
tcm2_pcrread <pcr>
```

**参数说明**：

- pcr：(可选)PCR寄存器索引（0-23），如果无参数则打印出所有的pcr值。

**示例**：

```shell
PMON> tcm2_pcrread 16  # 读取PCR 16的值
```

**注意事项**：

- PCR索引必须在0-23范围内
- 使用SM3-256哈希算法
- 读取的哈希值为32字节长度
- 命令会同时返回PCR更新次数

**返回值**：

- 成功：显示PCR内容、更新次数和成功信息
- 失败：显示错误代码和失败信息

## 密钥管理命令

### tcm2_createprimary

**功能描述**：在TCM2.0中创建主密钥。主密钥是密钥层级结构的根密钥，用于派生其他密钥。

**用法**：

```shell
tcm2_createprimary <type>
```

**参数说明**：

- type：密钥类型
  - hmac：创建用于HMAC运算的密钥
  - sign：创建用于签名的SM2密钥
  - sm2：创建用于加解密的SM2密钥
  - sm4：创建用于对称加解密的SM4密钥

**返回信息**：

- 成功：显示创建的主密钥句柄（handle）
- 失败：显示错误代码

**示例**：

```shell
PMON> tcm2_createprimary hmac  # 创建HMAC主密钥
PMON> tcm2_createprimary sm2   # 创建SM2主密钥
```

### tcm2_create

**功能描述**：在指定的父密钥下创建子密钥。这些密钥可用于签名、加密等操作。由于在仅有主密钥的情况下也可进行加解密、签名等操作，因此可直接使用tcm2_createPrimary生成的句柄进行操作，本示例展现的是基于签名和验签的create以及load。

**用法**：

```shell
tcm2_create <parent_handle>
```

**参数说明**：

- parent_handle：父密钥的句柄（16进制格式）
  - 例如：0x80000000

**返回信息**：

- 成功：显示父密钥句柄
- 失败：显示错误代码和详细错误信息

**示例**：

```shell
PMON> tcm2_create 0x80000000  # 在句柄为0x80000000的父密钥下创建子密钥
```

### tcm2_load

**功能描述**：将已创建的密钥加载到TCM2.0中，使其可以用于后续操作。

**用法**：

```shell
tcm2_load <parent_handle>
```

**参数说明**：

- parent_handle：父密钥的句柄（16进制格式）
  - 例如：0x80000000

**返回信息**：

- 成功：显示加载后的密钥句柄
- 失败：显示错误代码和详细错误信息

**示例**：

```shell
PMON> tcm2_load 0x80000000  # 加载父密钥句柄为0x80000000的密钥
```

**密钥管理流程**：

1. 使用`tcm2_createPrimary`创建主密钥
2. 使用`tcm2_create`在主密钥下创建子密钥
3. 使用`tcm2_load`加载创建的密钥
4. 使用`tcm2_sm2encryptdecrypt`或`tcm2_encryptdecrypt`进行加解密操作
5. 操作完成后可使用`tcm2_flushcontext`清除密钥

## 加密解密命令

### tcm2_encryptdecrypt(SM4对称加解密)

**功能描述**：使用TCM2.0执行SM4对称加密或解密操作。本示例是将message数据连续的进行加密和解密。

**用法**：

```shell
tcm2_encryptdecrypt [-enc|-dec] <handle> [message]
tcm2_encryptdecrypt [-enc|-dec] <handle> <address> <size>
```

**参数说明**：

- -enc, --encrypt：可选参数，仅执行加密操作
- -dec, --decrypt：可选参数，仅执行解密操作（需要之前已执行过加密）
- handle：SM4密钥的句柄（16进制格式）
- message：可选参数，要加密的消息
  - 如果不提供，使用默认测试消息
- address：内存地址，存储要加密的数据
- size：要加密的数据大小（字节）

**功能特点**：

- 使用SM4算法
- 支持CFB加密模式
- 支持单独加密或解密模式
- 默认模式下自动执行加密和解密操作进行验证
- 显示原文、密文和解密后的数据

**重要警告**：
- 加密后的密钥句柄仅在当前会话中有效
- 系统重启或执行`tcm2_flushcontext`命令后，密钥句柄将失效
- 如果密钥句柄丢失，将无法解密之前加密的数据
- 如果需要长期保存加密数据，请记录并妥善保管密钥信息

**示例**：

```shell
PMON> tcm2_encryptdecrypt 0x80000000                      # 使用默认消息进行加解密
PMON> tcm2_encryptdecrypt 0x80000000 "hello world"        # 使用指定内容进行加解密
PMON> tcm2_encryptdecrypt 0x80000000 0x900000000b023d60 32  # 加密指定内存地址中32字节数据

PMON> tcm2_encryptdecrypt -enc 0x80000000 "Hello World"   # 仅加密指定消息
PMON> tcm2_encryptdecrypt -enc 0x80000000 0x900000000b023d60 32  # 仅加密指定内存地址中32字节数据

PMON> tcm2_encryptdecrypt -dec 0x80000000                 # 仅解密之前加密的数据
PMON> tcm2_encryptdecrypt -dec 0x80000000 0x900000000b023d60 32  # 仅解密指定内存地址中32字节数据
```

**返回信息**：

- 成功时显示：
  - 加密后的数据
  - 原始明文
  - 解密后的数据
- 失败时显示错误代码

### tcm2_sm2encryptdecrypt(SM2非对称加解密)

**功能描述**：使用TCM2.0执行SM2非对称加密或解密操作。本示例是将message数据连续的进行加密和解密。

**用法**：

```shell
tcm2_sm2encryptdecrypt [-enc|-dec] <handle> [message]
tcm2_sm2encryptdecrypt [-enc|-dec] <handle> <address> <size>
```

**参数说明**：

- -enc, --encrypt：可选参数，仅执行加密操作
- -dec, --decrypt：可选参数，仅执行解密操作（需要之前已执行过加密）
- handle：SM2密钥的句柄（16进制格式）
- message：可选参数，要加密的消息
  - 如果不提供，使用默认测试消息
- address：内存地址，存储要加密的数据
- size：要加密的数据大小（字节）

**功能特点**：

- 使用SM2公钥算法
- 支持C1C2C3格式的密文
- 支持单独加密或解密模式
- 默认模式下自动执行加密和解密操作进行验证
- 显示明文和密文数据

**重要警告**：
- 加密后的密钥句柄仅在当前会话中有效
- 系统重启或执行`tcm2_flushcontext`命令后，密钥句柄将失效
- 如果密钥句柄丢失，将无法解密之前加密的数据
- 如果需要长期保存加密数据，请记录并妥善保管密钥信息

**示例**：

```shell
PMON> tcm2_sm2encryptdecrypt 0x80000000                      # 使用默认消息进行加解密
PMON> tcm2_sm2encryptdecrypt 0x80000000 "hello world"        # 使用特定消息进行加解密
PMON> tcm2_sm2encryptdecrypt 0x80000000 0x900000000b023d60 24    # 使用指定内存地址的24字节数据进行加解密

PMON> tcm2_sm2encryptdecrypt -enc 0x80000000 "Hello World"   # 仅加密指定消息
PMON> tcm2_sm2encryptdecrypt -enc 0x80000000 0x900000000b023d60 24  # 仅加密指定内存地址中24字节数据

PMON> tcm2_sm2encryptdecrypt -dec 0x80000000                 # 仅解密之前加密的数据
PMON> tcm2_sm2encryptdecrypt -dec 0x80000000 0x900000000b023d60 24  # 仅解密指定内存地址中24字节数据
```

**返回信息**：

- 成功时显示：
  - 明文数据
  - 密文数据（C1C2C3格式）
  - 解密后的明文
- 失败时显示错误代码

**加密解密流程**：

1. 使用`tcm2_createPrimary`创建SM2或SM4主密钥
2. 使用`tcm2_sm2encryptdecrypt`或`tcm2_encryptdecrypt`进行加解密操作
3. 使用完毕后可用`tcm2_flushcontext`清除密钥

### tcm2_getcap

**功能描述**：获取TCM2.0设备的能力信息，包括支持的算法、属性等。可以查询特定算法或列出所有支持的算法。本示例只对算法进行了适配，属性的查询可参照 tcm_nv.c 中的capability操作。

**用法**：

```shell
tcm2_getcap <capability> <property> <addr> <count> [<search_alg_id>]
```

**参数说明**：

- capability：能力类型（如0x00000001）
- property：属性值（如0x80000000）
- addr：存储返回数据的内存地址（每个条目4字节）
  - 例如：0x900000000b023d80
- count：要获取的条目数量（如10）
- search_alg_id：可选参数，用于搜索特定算法ID（十六进制格式）

**返回数据格式**：

- 每个条目包含：
  - 算法ID（低16位）
  - 属性值（高16位）

**示例**：

```shell
PMON> tcm2_getcap 0x00000001 0x80000000 0x900000000b023d80 10         # 列出所有支持的算法
PMON> tcm2_getcap 0x00000001 0x80000000 0x900000000b023d80 10 0x3d60  # 查找特定算法
```

**返回信息**：

- 不指定算法ID时：列出所有支持的算法及其属性
- 指定算法ID时：显示是否找到该算法及其属性

## 字典攻击防护命令

### tcm2_damreset

**功能描述**：重置TCM2.0的字典攻击计数器。只有在TCM未处于锁定状态时才能执行此操作。

**用法**：

```shell
tcm2_damreset [<password>]
```

**参数说明**：

- password：可选的LOCKOUT层级授权密码
  - 密码长度不能超过TCM2_DIGEST_LEN

**示例**：

```shell
PMON> tcm2_damreset              # 不使用密码重置
PMON> tcm2_damreset mypassword   # 使用密码重置
```

### tcm2_damparameters

**功能描述**：设置TCM2.0的字典攻击缓解（DAM）参数。这些参数控制TCM的锁定行为。

**用法**：

```shell
tcm2_damparameters <maxTries> <recoveryTime> <lockoutRecovery> [<password>]
```

**参数说明**：

- maxTries：锁定前的最大失败次数
  - 0表示始终锁定
- recoveryTime：错误计数器递减前的时间
  - 0表示不锁定
- lockoutRecovery：锁定状态的持续时间
  - 0表示需要重启才能解锁
- password：可选的LOCKOUT层级授权密码
  - 密码长度不能超过TCM2_DIGEST_LEN

**注意事项**：

- 只有在TCM未处于锁定状态时才能修改这些参数
- 所有时间参数的单位都是秒
- 参数设置不当可能导致TCM长时间锁定

**示例**：

```shell
PMON> tcm2_damparameters 5 300 3600           # 设置：5次失败/5分钟恢复/1小时锁定
PMON> tcm2_damparameters 3 600 7200 password  # 使用密码设置：3次失败/10分钟恢复/2小时锁定
```

## 授权管理命令

### tcm2_changeauth

**功能描述**：更改TCM2.0指定层级的授权密码。可以为不同的层级（如平台、所有者、背书和锁定）设置不同的授权值。

**用法**：

```shell
tcm2_changeauth <hierarchy> <new_pw> [<old_pw>]
```

**参数说明**：

- hierarchy：TCM层级
  - TCM2_RH_LOCKOUT：锁定层级
  - TCM2_RH_ENDORSEMENT：背书层级
  - TCM2_RH_OWNER：所有者层级
  - TCM2_RH_PLATFORM：平台层级
- new_pw：新的授权密码
  - 密码长度不能超过TCM2_DIGEST_LEN
- old_pw：可选的原授权密码
  - 密码长度不能超过TCM2_DIGEST_LEN

**注意事项**：

- 更改授权值后请务必记住新密码
- 如果忘记密码，可能需要清除TCM才能重新使用
- 不同层级的授权用于不同的操作权限控制

**示例**：

```shell
PMON> tcm2_changeauth TCM2_RH_OWNER newpass              # 设置所有者层级新密码
PMON> tcm2_changeauth TCM2_RH_LOCKOUT newpass oldpass    # 更改锁定层级密码
```

## 随机数生成命令

### tcm2_getrandom

**功能描述**：从TCM2.0硬件获取真随机数。TCM2.0提供高质量的硬件随机数生成器，可用于密码学应用。

**用法**：

```shell
tcm2_getrandom <count>
tcm2_getrandom <count> <addr>
```

**参数说明**：

- count：要生成的随机字节数
- addr：可选参数，存储随机数的内存地址
  - 如果不提供，将直接显示生成的随机数

**返回信息**：

- 成功时：
  - 显示生成的随机字节数
  - 显示存储地址（如果提供）
  - 以十六进制格式打印随机数据
- 失败时：显示错误代码

**示例**：

```shell
PMON> tcm2_getrandom 32                      # 生成32字节随机数并直接显示
PMON> tcm2_getrandom 32 0x900000000b023d60   # 生成32字节随机数并存储在指定地址
```

**调试信息**：
命令执行过程中会显示详细的调试信息：

- 输入参数解析结果
- 内存地址转换信息
- 操作结果状态

## 签名验证命令

### tcm2_sigver

**功能描述**：使用TCM2.0执行SM2数字签名和验证操作。SM2是中国国家密码管理局发布的椭圆曲线公钥密码算法。

**用法**：

```shell
tcm2_sigver [-s|-v] <handle> [memory_address size]
```

**参数说明**：

- -s, --sign：可选参数，仅执行签名操作
- -v, --verify：可选参数，仅执行验证操作（需要之前已执行过签名）
- handle：SM2签名密钥的句柄（16进制格式）
  - 例如：0x80000000
- memory_address：可选参数，存储要签名/验证的数据的内存地址
- size：要签名/验证的数据大小（字节，必须为32字节）

**功能特点**：

- 使用SM2签名算法
- 支持单独签名或验证操作
- 默认模式下自动执行签名和验证操作
- 支持32字节消息摘要
- 生成SM2标准的r,s签名值

**操作流程**：

1. 对输入数据进行签名（如果未指定-v）
2. 显示签名值的r和s部分（如果执行了签名）
3. 使用公钥验证签名（如果未指定-s）
4. 显示验证结果

**返回信息**：

- 签名成功时显示：
  - 签名值r部分
  - 签名值s部分
- 验证成功时显示：
  - 验证成功信息
- 失败时显示错误代码和详细错误信息

**示例**：

```shell
PMON> tcm2_sigver 0x80000000                   # 使用默认数据进行签名和验证
PMON> tcm2_sigver -s 0x80000000                # 仅对默认数据进行签名
PMON> tcm2_sigver -v 0x80000000                # 仅验证之前签名的数据
PMON> tcm2_sigver 0x80000000 0x900000000b023d60 32  # 签名和验证指定内存地址中32字节数据
PMON> tcm2_sigver -s 0x80000000 0x900000000b023d60 32  # 仅签名指定内存地址中32字节数据
PMON> tcm2_sigver -v 0x80000000 0x900000000b023d60 32  # 仅验证指定内存地址中32字节数据
```

**签名验证流程**：

1. 使用`tcm2_createprimary`创建SM2主密钥
2. 使用`tcm2_create`创建用于签名的子密钥
3. 使用`tcm2_load`加载签名密钥
4. 使用`tcm2_sigver`进行签名和验证操作
5. 使用完毕后可用`tcm2_flushcontext`清除密钥

## 会话管理命令

### tcm2_flushcontext

**功能描述**：清除TCM2.0中指定句柄的上下文。用于释放TCM资源，包括密钥、会话等。

**用法**：

```shell
tcm2_flushcontext <handle>
```

**参数说明**：

- handle：要清除的句柄（16进制格式）
  - 例如：0x80000000
  - 可以是密钥句柄、会话句柄等

**注意事项**：

- 清除后句柄将无法继续使用
- 建议在完成操作后及时清除不再使用的句柄
- 清除前确保相关操作已完成

**返回信息**：

- 成功：无输出
- 失败：显示错误代码和详细错误信息

**示例**：

```shell
PMON> tcm2_flushcontext 0x80000000  # 清除句柄为0x80000000的上下文
```

**使用场景**：

1. 清除不再使用的密钥句柄
2. 清除已完成的会话句柄
3. 释放TCM资源以供其他操作使用
4. 安全考虑，清除敏感操作的上下文

## 哈希运算命令

### tcm2_hash

**功能描述**：使用TCM2.0硬件加速的SM3算法计算哈希值。支持性能测试和进度显示。

**用法**：

```shell
tcm2_hash [-r] [message]
tcm2_hash [-r] <address> <size>
```

**参数说明**：

- -r：可选参数，显示性能测试信息（数据大小、算法类型、处理时间、速率等）
  - 可以放在命令的任何位置
- message：可选参数，要计算哈希的消息
  - 如果不提供，使用默认测试消息
- address：内存地址，存储要计算哈希的数据
- size：要计算哈希的数据大小（字节）

**算法特性**：

- 数据 > 1024字节：自动使用分片处理（硬件分片大小：1KB）
- 数据 ≤ 1024字节：使用直接处理
- 支持进度显示：每10个分片显示一个点，每16个点换行

**示例**：

```shell
PMON> tcm2_hash                            # 使用默认消息计算SM3哈希值
PMON> tcm2_hash "Hello World"              # 计算指定消息的SM3哈希值
PMON> tcm2_hash 0x900000000b02cc00 32      # 计算指定内存地址中32字节数据的哈希值
PMON> tcm2_hash -r 0x900000000b02cc00 5242880  # 计算5MB数据并显示性能信息
PMON> tcm2_hash 0x900000000b02cc00 -r 1048576  # -r参数可以放在任何位置
```

### tcm2_hash_sw

**功能描述**：使用软件实现的SM3算法计算哈希值。支持性能测试和进度显示。

**用法**：

```shell
tcm2_hash_sw [-r] [message]
tcm2_hash_sw [-r] <address> <size>
```

**参数说明**：

- -r：可选参数，显示性能测试信息（数据大小、算法类型、处理时间、速率等）
  - 可以放在命令的任何位置
- message：可选参数，要计算哈希的消息
  - 如果不提供，使用默认测试消息
- address：内存地址，存储要计算哈希的数据
- size：要计算哈希的数据大小（字节）

**算法特性**：

- 数据 > 40960字节：自动使用分片处理（软件分片大小：40KB）
- 数据 ≤ 40960字节：使用直接处理
- 支持进度显示：每个分片显示一个点

**示例**：

```shell
PMON> tcm2_hash_sw                         # 使用默认消息计算SM3哈希值
PMON> tcm2_hash_sw "Hello World"           # 计算指定消息的SM3哈希值
PMON> tcm2_hash_sw 0x900000000b02cc00 32   # 计算指定内存地址中32字节数据的哈希值
PMON> tcm2_hash_sw -r 0x900000000b02cc00 5242880  # 计算5MB数据并显示性能信息
```

**软硬件实现对比**：

1. **性能**：硬件实现通常更快，但软件实现使用更大的分片减少TCM通信开销
2. **分片大小**：硬件1KB vs 软件40KB
3. **安全性**：硬件实现更安全，不易受到软件攻击
4. **资源占用**：软件实现占用CPU资源，硬件实现使用专用电路
5. **应用场景**：
   - 软件实现适用于大数据量处理或测试场景
   - 硬件实现适用于需要安全保证的正式环境

### tcm2_hashsequance_test

**功能描述**：测试TCM2.0的分片哈希功能，使用6144字节测试数据验证硬件分片处理能力。

**用法**：

```shell
tcm2_hashsequance_test
```

**功能特性**：

- 使用固定的6144字节测试数据
- 验证硬件分片哈希处理流程
- 显示完整的哈希计算结果

**示例**：

```shell
PMON> tcm2_hashsequance_test  # 执行分片哈希测试
```

## HMAC运算命令

### tcm2_hmac_sw

**功能描述**：使用软件实现的HMAC-SM3算法计算消息认证码。

**用法**：

```shell
tcm2_hmac_sw [key] [message]
```

**参数说明**：

- key：可选参数，用于HMAC计算的密钥
  - 如果不提供，使用默认测试密钥
- message：可选参数，要计算HMAC的消息
  - 如果不提供，使用默认测试消息

**示例**：

```shell
PMON> tcm2_hmac_sw                           # 使用默认密钥和消息计算HMAC值
PMON> tcm2_hmac_sw "MySecret"                # 使用自定义密钥和默认消息
PMON> tcm2_hmac_sw "MySecret" "Hello World"  # 使用自定义密钥和消息
```

**返回信息**：

- 成功时显示：
  - HMAC计算结果（32字节十六进制）
- 失败时显示具体错误阶段和错误信息

### tcm2_hmac

**功能描述**：使用TCM2.0硬件加速的HMAC-SM3算法计算消息认证码。

**用法**：

```shell
tcm2_hmac <handle> [message]
tcm2_hmac <handle> <address> <size>
```

**参数说明**：

- handle：HMAC密钥的句柄（16进制格式）
- message：可选参数，要计算HMAC的消息
  - 如果不提供，使用默认测试消息
- address：内存地址，存储要计算HMAC的数据
- size：要计算HMAC的数据大小（字节）

**示例**：

```shell
PMON> tcm2_hmac 0x80000000                  # 使用默认消息计算HMAC值
PMON> tcm2_hmac 0x80000000 "Hello World"    # 计算指定消息的HMAC值
PMON> tcm2_hmac 0x80000000 0x900000000b023d60 32  # 计算指定内存地址中32字节数据的HMAC值
```

**软硬件实现对比**：

1. 密钥管理：
   - 软件实现使用明文密钥
   - 硬件实现使用TCM保护的密钥
2. 安全性：
   - 硬件实现提供更好的密钥保护
   - 硬件实现防止密钥泄露
3. 使用场景：
   - 软件实现适用于测试和开发
   - 硬件实现适用于生产环境

## NV存储命令

### tcm2_nvdefine

**功能描述**：在TCM2.0中定义一个新的NV存储空间。每个NV空间都有唯一的索引和指定的大小。

**用法**：

```shell
tcm2_nvdefine [index] [size]
```

**参数说明**：

- index：可选参数，指定NV空间的索引(以下三个特殊索引非必要不能写入数据，除非是特定的策略数据，否则可能会导致主板无法正常工作)
  - 0x013ffffe：用于哈希配置，大小固定为 0x20c（524字节）--------哈希策略
  - 0x013fffff：用于报告，大小固定为 0x40（64字节）----------度量输出
  - 0x013ffffd：用于签名验证配置，大小固定为 0x104（260字节）--------签名策略
  - 其他：从 0x1000000 开始自动分配，默认大小32字节
- size：可选参数，指定NV空间的大小（字节）
  - 默认大小：32字节
  - 最大大小：800字节
  - 如果指定了特殊索引，则使用该索引对应的固定大小

**注意事项**：

- 特殊索引（0x013ffffe、0x013fffff、0x013ffffd）使用 TCM2_RH_PLATFORM 权限
- 普通索引使用 TCM2_RH_OWNER 权限
- 如果定义失败，可以尝试使用 tcm2_clear 命令清除TCM后重试
- NV空间索引从0x1000000开始递增

**示例**：

```shell
PMON> tcm2_nvdefine                  # 使用默认大小定义NV空间
PMON> tcm2_nvdefine 64              # 定义64字节的NV空间
PMON> tcm2_nvdefine 0x01000000 0x20 # 定义指定索引和大小的NV空间
```

### tcm2_nvwrite

**功能描述**：向指定的NV空间写入数据。

**用法**：

```shell
tcm2_nvwrite <index> <data> [size]
tcm2_nvwrite <index> <address> <size>
```

**参数说明**：

- index：NV空间的索引
- data：要写入的字符串数据，或
- address：要写入数据的内存地址
- size：要写入的数据大小（字节）

**示例**：

```shell
PMON> tcm2_nvwrite 0x1000000 "test data"                    # 写入字符串
PMON> tcm2_nvwrite 0x013ffffe 0x8000000082000000 523        # 哈希校验，或者 0x8000000082100000 260，这是签名的策略
```

### tcm2_nvread

**功能描述**：读取指定NV空间的数据。

**用法**：

```shell
tcm2_nvread <index>
```

**参数说明**：

- index：NV空间的索引

**返回信息**：

- 成功时显示：原始数据（十六进制格式）

### tcm2_nvreadpublic

**功能描述**：读取NV空间的公共信息。

**用法**：

```shell
tcm2_nvreadpublic [index]
```

**参数说明**：

- index：可选参数，NV空间的索引
  - 如果不指定，将显示所有已定义NV空间的信息

**返回信息**：

- NV空间的索引
- 名称（name）
- 哈希算法（SM3-256）
- 属性（authwrite|authread）
- 空间大小

### tcm2_nvundefine

**功能描述**：删除指定的NV空间。

**用法**：

```shell
tcm2_nvundefine <index>
```

**参数说明**：

- index：要删除的NV空间索引

**注意事项**：

- 特殊索引（0x013ffffe、0x013fffff、0x013ffffd）需要 TCM2_RH_PLATFORM 权限，代码已自动配置
- 普通索引需要 TCM2_RH_OWNER 权限

**示例**：

```shell
PMON> tcm2_nvundefine 0x1000000
```

## 测试命令

### tcm2_test

**功能描述**：执行TCM2.0的综合测试，包括密钥创建、加载、签名验证等完整流程。

**用法**：

```shell
tcm2_test
```

**测试流程**：

1. 创建SM2主密钥
2. 创建子密钥
3. 加载密钥
4. 执行签名操作
5. 执行验证操作
6. 显示测试结果

**示例**：

```shell
PMON> tcm2_test  # 执行完整的TCM2.0功能测试
```

## 文件系统测试命令

### test_emmc_files

**功能描述**：测试eMMC文件系统访问功能，验证文件读取能力。

**用法**：

```shell
test_emmc_files
```

**功能特性**：

- 测试预定义的关键文件访问
- 验证文件系统挂载状态
- 显示文件访问结果

**示例**：

```shell
PMON> test_emmc_files  # 测试eMMC文件系统访问
```

### list_emmc_files

**功能描述**：列出eMMC目录内容，扫描多个分区类型。

**用法**：

```shell
list_emmc_files
```

**功能特性**：

- 扫描ext2、ext4等文件系统分区
- 显示目录结构和文件列表
- 自动检测可用的文件系统

**示例**：

```shell
PMON> list_emmc_files  # 列出eMMC目录内容
```

### test_emmc_sm3

**功能描述**：测试eMMC文件的SM3哈希计算功能。

**用法**：

```shell
test_emmc_sm3
```

**功能特性**：

- 对关键文件计算SM3哈希值
- 验证哈希计算功能
- 显示计算结果和性能信息

**示例**：

```shell
PMON> test_emmc_sm3  # 测试eMMC文件SM3哈希计算
```

### measure_critical_files

**功能描述**：测量关键文件并进行完整性验证。

**用法**：

```shell
measure_critical_files
```

**功能特性**：

- 计算关键文件的哈希值
- 与NV存储中的基准值比较
- 提供完整性验证结果

**示例**：

```shell
PMON> measure_critical_files  # 测量关键文件完整性
```

## 环境变量度量命令

### tcm_env_restore

**功能描述**：增加实现环境变量度量功能：在环境变量加载前直接从Flash读取原始数据，计算SM3哈希并与TCM NV中的基线比较，检测篡改。此命令用于手动更新环境变量的基线哈希值。

**用法**：

```shell
tcm_env_restore
```

**功能特性**：

- 直接从Flash的NVRAM偏移地址读取环境变量原始数据
- 使用SM3算法计算哈希值
- 删除现有基线并存储新的基线哈希到TCM NV存储
- 自动适配NAND启动(0x000ffc00)和Flash启动(0x000ff000)模式

**示例**：

```shell
PMON> tcm_env_restore  # 更新环境变量基线
```

**返回信息**：

- 成功：显示新基线哈希值
- 失败：显示具体错误信息

### tcm_env_status

**功能描述**：显示当前环境变量度量状态，包括当前哈希值、基线哈希值和比较结果。

**用法**：

```shell
tcm_env_status
```

**功能特性**：

- 显示当前环境变量的SM3哈希值
- 显示存储在TCM NV中的基线哈希值
- 显示比较结果和状态信息
- 提供操作建议

**示例**：

```shell
PMON> tcm_env_status  # 查看环境变量度量状态
```

**返回信息**：

- **VERIFIED**：当前环境变量与基线匹配
- **MISMATCH**：环境变量已发生变化
- **UNINITIALIZED**：尚未建立基线
- **ERROR**：读取基线失败

**注意事项**：

- 环境变量度量在PMON启动时自动执行
- 度量在`envinit()`之前进行，确保度量原始Flash数据
- 使用TCM NV索引 0x012fff00 存储基线哈希
- 支持条件编译控制(`TCM_ENV_MEASUREMENT`宏)

## 安全度量功能

### 自动文件完整性度量

当定义了 `TCM2` 和 `LS2_SE` 宏时，系统会在以下场景自动执行文件完整性度量：

#### 内核文件度量

**触发场景**：
- `load` 命令加载 vmlinuz/vmlinux 文件时
- `boot` 命令启动内核文件时

**度量流程**：
1. 自动检测文件类型（vmlinuz/vmlinux）
2. 计算文件的 SM3 哈希值
3. 将哈希值存储到 TCM NV 索引 `0x012ffffe`（`TCM2_METRIC_VMLINUZ`）
4. 显示计算时间和性能信息

**示例输出**：
```
LS2_SE: Starting security measurement for vmlinuz: /path/to/vmlinuz
LS2_SE: vmlinuz hash calculation successful
LS2_SE: Calculation time: 5s
```

#### 文件系统度量策略

根据 `MEASURE_CRITICAL_FILES` 宏的设置，系统采用不同的度量策略：

**策略1：关键文件度量** (`MEASURE_CRITICAL_FILES=1`)
- 度量预定义的关键文件列表
- 每个文件使用独立的 NV 索引存储哈希值
- 适用于精确控制度量范围的场景

**策略2：文件系统镜像度量** (`MEASURE_CRITICAL_FILES=0`，默认)
- 度量整个文件系统镜像文件
- 使用 TCM NV 索引 `0x012ffffd`（`TCM2_METRIC_FILESYSTEM`）存储哈希值
- 适用于整体文件系统完整性验证

#### 度量失败处理

根据 `STOP_ON_MEASURE_FAILURE` 宏的设置：

**严格模式** (`STOP_ON_MEASURE_FAILURE=1`)
- 度量失败时立即停止启动过程
- 返回错误代码，阻止系统继续运行
- 适用于高安全要求的环境

**宽松模式** (`STOP_ON_MEASURE_FAILURE=0`，默认)
- 度量失败时记录警告信息但继续启动
- 允许系统在度量异常时正常运行
- 适用于开发和测试环境

### NV 存储索引分配

| 功能 | NV 索引 | 宏定义 | 用途 |
|------|---------|--------|------|
| 内核文件哈希 | 0x012ffffe | TCM2_METRIC_VMLINUZ | 存储 vmlinuz/vmlinux 文件哈希 |
| 文件系统哈希 | 0x012ffffd | TCM2_METRIC_FILESYSTEM | 存储文件系统镜像哈希 |
| 环境变量哈希 | 0x012fff00 | TCM2_METRIC_ENV_VARS | 存储环境变量基线哈希 |

## 关键功能总结

```shell
# 手动初始化
tcm_init;tcm2_startup -c

# sign && verify
tcm2_createprimary sign
tcm2_create 0x80000000
tcm2_load 0x80000000
tcm2_sigver 0x80000001                               # 签名并验证默认数据
tcm2_sigver -s 0x80000001                            # 仅执行签名
tcm2_sigver -v 0x80000001                            # 仅执行验签
tcm2_sigver 0x80000001 0x900000000b023d60 32         # 签名并验证指定内存数据
tcm2_sigver -s 0x80000001 0x900000000b023d60 32      # 仅对指定内存数据签名
tcm2_sigver -v 0x80000001 0x900000000b023d60 32      # 仅验证指定内存数据
tcm2_flushcontext 0x80000000;tcm2_flushcontext 0x80000001

# sm3 && hmac (硬件实现)
tcm2_hash "hello world"
tcm2_hash 0x900000000b023d60 32
tcm2_hash -r 0x900000000b02cc00 5242880          # 性能测试：5MB数据
tcm2_createprimary hmac
tcm2_hmac 0x80000000 "hello world"
tcm2_hmac 0x80000000 0x900000000b023d60 32
tcm2_flushcontext 0x80000000

# sm3 && hmac (软件实现)
tcm2_hash_sw "hello world"
tcm2_hash_sw -r 0x900000000b02cc00 5242880       # 性能测试：5MB数据
tcm2_hmac_sw "MySecret" "Hello World"

# 哈希测试
tcm2_hashsequance_test                           # 分片哈希测试

# sm2 && sm4
tcm2_createprimary sm2
tcm2_sm2encryptdecrypt 0x80000000
tcm2_sm2encryptdecrypt 0x80000000 "hello world"
tcm2_sm2encryptdecrypt 0x80000000 0x900000000b023d60 24
tcm2_sm2encryptdecrypt -enc 0x80000000 "hello world"
tcm2_sm2encryptdecrypt -dec 0x80000000
tcm2_flushcontext 0x80000000

tcm2_createprimary sm4
tcm2_encryptdecrypt 0x80000000
tcm2_encryptdecrypt 0x80000000 "hello world"
tcm2_encryptdecrypt 0x80000000 0x900000000b023d60 32
tcm2_encryptdecrypt -enc 0x80000000 "hello world"
tcm2_encryptdecrypt -dec 0x80000000
tcm2_flushcontext 0x80000000

# random
tcm2_getrandom 32 
tcm2_getrandom 32 0x900000000b023d60 # tcm2_getrandom size addr
d8 0x900000000b023d60  4

# PCR
tcm2_pcrread
tcm2_pcrextend 16:sm3_256=44f0061e69fa6fdfc290c494654a05dc0c053da7e5c52b84ef93a9d67d3fff88
tcm2_pcrread 0,16,22
tcm2_pcrreset 16

# NV 一次最大创建800字节的空间
# 遇到失败，尝试使用tcm2_clear 清除
tcm2_nvdefine 64
tcm2_nvreadpublic
tcm2_nvwrite 0x1000000 "Hello World.Hello World.Hello World.Hello World.Hello World."
tcm2_nvwrite 0x1000000 0x900000000b023d60 32
tcm2_nvread 0x1000000
tcm2_nvundefine 0x1000000

# 一键式创建主密钥、子密钥、加载、签名、验签
tcm2_test
tcm2_flushcontext 0x80000000
tcm2_flushcontext 0x80000001

# 文件系统测试
test_emmc_files                                  # 测试eMMC文件访问
list_emmc_files                                  # 列出eMMC目录内容
test_emmc_sm3                                    # 测试eMMC文件SM3哈希
measure_critical_files                          # 测量关键文件完整性

# 环境变量度量
tcm_env_restore                                  # 更新环境变量基线
tcm_env_status                                   # 查看环境变量度量状态
```