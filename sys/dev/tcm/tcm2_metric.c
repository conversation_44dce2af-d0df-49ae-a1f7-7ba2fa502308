#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <tcm-v2.h>
#include <pmonenv.h>
#include <pmon.h>
#include <pflash.h>
#include "tcm2_metric.h"
#include "tcm2_sm3.h"
#include "tcm2_log.h"
#include "tcm-utils.h"
#include "target/bonito.h"  /* Include NVRAM_OFFS and NVRAM_SIZE definitions */

extern u32 tcm2_nv_define_space_impl(u32 index, u32 size);
extern u32 tcm2_nv_read_impl(u32 index, void *data, size_t data_len, size_t *new_size);
extern u32 tcm2_nv_write_impl(u32 index, const void *data, size_t data_len);
extern u32 tcm2_nv_readpublic_impl(u32 index);
extern u32 tcm2_nv_undefine_space_impl(u32 space_index);

/* SM3 hardware algorithm function declarations */
extern u32 tcm2_hash_init(u16 hash_alg, u32 *handle);
extern u32 tcm2_hash_update(u32 handle, u8 *data, u32 data_len);
extern u32 tcm2_hash_final(u32 handle, u8 *remain, u32 remain_len, u8 *digest);

/* ========================================================================
 * NV space measurement operation functions
 * ======================================================================== */

int tcm2_metric_nv_exists(u32 nv_index) {
    u32 rc = tcm2_nv_readpublic_impl(nv_index);
    if (rc == 0) {
        return 1;  // NV space exists
    } else {
        // printf("Failed to read NV public info, error code: 0x%08x\n", rc);
        return 0;  // NV space does not exist
    }
}

int tcm2_metric_nv_write(u32 nv_index, const u8 *hash_data) {
    if (!hash_data) {
        return -1;
    }
    return tcm2_nv_write_impl(nv_index, hash_data, TCM2_HASH_SIZE);
}

int tcm2_metric_nv_read(u32 nv_index, u8 *hash_data) {
    if (!hash_data) {
        return -1;
    }

    size_t new_size = 0;
    u32 rc = tcm2_nv_read_impl(nv_index, hash_data, TCM2_HASH_SIZE, &new_size);
    if (rc == 0 && new_size == TCM2_HASH_SIZE) {
        return 0;
    }
    printf("Failed to read NV space. Error code: 0x%08X\n", rc);
    return -1;
}

int tcm2_metric_nv_delete(u32 nv_index) {
    return tcm2_nv_undefine_space_impl(nv_index);
}

int tcm2_metric_store_or_verify(u32 nv_index, const u8 *computed_hash) {
    if (!computed_hash) {
        printf("Error: computed hash is NULL\n");
        return -1;
    }
    u32 rc;
    int nv_exists = tcm2_metric_nv_exists(nv_index);
    if (nv_exists == 0) {
        rc = tcm2_nv_define_space_impl(nv_index, TCM2_HASH_SIZE);
        if(rc != nv_index){
            printf("Failed to define NV space. Error code: 0x%08X\n", rc);
            return -1;
        }
        rc = tcm2_metric_nv_write(nv_index, computed_hash);
        if(rc != 0){
            printf("Failed to write NV space. Error code: 0x%08X\n", rc);
            return -1;
        }
        printf("Hash stored: 0x%08x\n", nv_index);
        return 0;
    } else {
        u8 stored_hash[TCM2_HASH_SIZE];
        rc = tcm2_metric_nv_read(nv_index, stored_hash);
        if(rc != 0){
            printf("Failed to read NV space. Error code: 0x%08X\n", rc);
            return -1;
        }

        if (memcmp(computed_hash, stored_hash, TCM2_HASH_SIZE) == 0) {
            return 0;
        } else {
            printf("[ERROE] Env Hash verification failed 0x%08x\n", nv_index);
            return 1;
        }
    }
}

/* ========================================================================
 * eMMC file testing and measurement functions
 * ======================================================================== */

static const emmc_file_config_t emmc_files_whitelist[] = {
    EMMC_CRITICAL_FILES_CONFIG
};

static const int emmc_files_count = sizeof(emmc_files_whitelist) / sizeof(emmc_file_config_t);

static int get_files(char file_paths[][MAX_FILE_PATH_LEN], int max_files) {
    int count = 0;
    for (int i = 0; i < emmc_files_count && count < max_files; i++) {
        snprintf(file_paths[count], MAX_FILE_PATH_LEN, "%s%s",
                EMMC_BASE_PATH, emmc_files_whitelist[i].filename);
        count++;
    }
    return count;
}

int tcm2_measure_critical_files(int argc, char **argv) {
    printf("Starting critical files measurement with NV storage\n");

    char file_paths[MAX_EMMC_FILES][MAX_FILE_PATH_LEN];
    int total_files = get_files(file_paths, MAX_EMMC_FILES);
    int success_count = 0;
    int error_count = 0;

    for (int i = 0; i < total_files; i++) {
        printf("Measuring file: %s (NV: 0x%08x)\n",
               emmc_files_whitelist[i].filename,
               emmc_files_whitelist[i].nv_index);

        u8 computed_hash[32];
        int hash_result = tcm2_unified_file_sm3_hash(file_paths[i], computed_hash);

        if (hash_result == 0) {
            // tcm_print_data("SM3 hash", computed_hash, 32);

            // Use NV storage to store or verify hash value
            int nv_result = tcm2_metric_store_or_verify(emmc_files_whitelist[i].nv_index, computed_hash);
            if (nv_result == 0) {
                printf("NV operation success for %s\n", emmc_files_whitelist[i].filename);
                success_count++;
            } else if (nv_result == 1) {
                printf("SECURITY ALERT: Hash verification failed for %s\n", emmc_files_whitelist[i].filename);
                error_count++;
            } else {
                printf("NV operation failed for %s\n", emmc_files_whitelist[i].filename);
                error_count++;
            }
        } else {
            printf("SM3 hash calculation failed for %s\n", emmc_files_whitelist[i].filename);
            error_count++;
        }
        printf("\n");
    }

    printf("Critical files measurement summary:\n");
    printf("Total files: %d, Success: %d, Error: %d\n",
           total_files, success_count, error_count);

    if (error_count == 0) {
        printf("All critical files measurement success\n");
        return 0;
    } else {
        printf("WARNING: %d critical files measurement failed\n", error_count);
        return -1;
    }
}

int tcm2_test_emmc_files(void) {
    printf("eMMC file system test\n");

    char file_paths[MAX_EMMC_FILES][MAX_FILE_PATH_LEN];
    int total_files = get_files(file_paths, MAX_EMMC_FILES);
    int working_files = 0;

    for (int i = 0; i < total_files; i++) {
        int fd = open(file_paths[i], O_RDONLY);
        if (fd >= 0) {
            unsigned char buffer[64];
            int bytes_read = read(fd, buffer, sizeof(buffer));
            if (bytes_read > 0) {
                working_files++;
                printf("File %d (%s): OK (%d bytes)\n", i+1,
                       emmc_files_whitelist[i].filename, bytes_read);
            } else {
                printf("File %d (%s): Read failed\n", i+1,
                       emmc_files_whitelist[i].filename);
            }
            close(fd);
        } else {
            printf("File %d (%s): Open failed\n", i+1,
                   emmc_files_whitelist[i].filename);
        }
    }

    printf("Working files: %d/%d\n", working_files, total_files);

    if (working_files > 0) {
        printf("eMMC file access working\n");
        return 0;
    } else {
        printf("eMMC file access failed\n");
        return -1;
    }
}

int tcm2_list_emmc_contents(void) {
    printf("eMMC Base path: %s\n", EMMC_BASE_PATH);
    printf("eMMC Directory contents:\n");
    char dir_path[MAX_FILE_PATH_LEN];
    sprintf(dir_path, "%s", EMMC_BASE_PATH);
    open(dir_path, O_RDONLY);

    printf("------------------------\n");
    printf("Critical files status:\n");
    char file_paths[MAX_EMMC_FILES][MAX_FILE_PATH_LEN];
    int total_files = get_files(file_paths, MAX_EMMC_FILES);
    int accessible_files = 0;

    for (int i = 0; i < total_files; i++) {
        int fd = open(file_paths[i], O_RDONLY);
        if (fd >= 0) {
            off_t file_size = lseek(fd, 0, SEEK_END);
            printf("  %s : Exist\n", emmc_files_whitelist[i].filename);
            accessible_files++;
            close(fd);
        } else {
            printf("  %s : Not found\n", emmc_files_whitelist[i].filename);
        }
    }
    return 0;
}

/* eMMC SM3 hash test function - only calculate and print hash values */
int tcm2_test_emmc_sm3_hash(void) {
    printf("eMMC files SM3 hash test\n");

    char file_paths[MAX_EMMC_FILES][MAX_FILE_PATH_LEN];
    int total_files = get_files(file_paths, MAX_EMMC_FILES);
    int success_count = 0;
    int error_count = 0;

    for (int i = 0; i < total_files; i++) {
        printf("Testing file: %s\n", emmc_files_whitelist[i].filename);

        u8 computed_hash[32];
        int hash_result = tcm2_unified_file_sm3_hash(file_paths[i], computed_hash);

        if (hash_result == 0) {
            success_count++;
            tcm_print_data("SM3 hash", computed_hash, 32);
        } else {
            error_count++;
            printf("SM3 hash calculation failed\n");
        }
        printf("\n");
    }

    printf("SM3 hash test summary:\n");
    printf("Total files: %d, Success: %d, Error: %d\n",
           total_files, success_count, error_count);

    if (error_count == 0) {
        printf("All files SM3 hash calculation success\n");
        return 0;
    } else {
        printf("WARNING: %d files SM3 hash calculation failed\n", error_count);
        return -1;
    }
}

/* Flash environment variable storage area definition */
/* Directly use NVRAM_OFFS and NVRAM_SIZE defined in bonito.h
 * These macros automatically select the correct offset address based on BOOT_FROM_NAND:
 * - BOOT_FROM_NAND: 0x000ffc00
 * - Normal Flash boot: 0x000ff000
 */
#define ENV_FLASH_OFFSET    NVRAM_OFFS
#define ENV_FLASH_SIZE      NVRAM_SIZE

extern struct fl_map *tgt_flashmap(void);

static int read_env_from_flash(u8 *buffer, u32 buffer_size, u32 *actual_size) {
    struct fl_map *map;
    char *flash_base;
    char *env_addr;
    u32 read_size;

    map = tgt_flashmap();
    if (!map) {
        printf("Failed to get flash map\n");
        return -1;
    }

    flash_base = (char *)map->fl_map_base;
    env_addr = flash_base + ENV_FLASH_OFFSET;

    printf("Flash base: 0x%p, Env offset: 0x%x, Env address: 0x%p\n",
           flash_base, ENV_FLASH_OFFSET, env_addr);

    read_size = (ENV_FLASH_SIZE < buffer_size) ? ENV_FLASH_SIZE : buffer_size;
    memcpy(buffer, env_addr, read_size);
    *actual_size = read_size;
    return 0;
}

static int tcm2_unified_memory_sm3_hash(const u8 *data, u32 data_size, u8 *hash_output) {
    if (!data || !hash_output) {
        printf("Error: invalid SM3 hash parameters\n");
        return -1;
    }

    if (data_size == 0) {
        printf("Warning: empty data for SM3 hash\n");
    }

#if USE_HARDWARE_SM3
    u32 handle = 0;
    u32 result = tcm2_hash_init(TCM2_ALG_SM3_256, &handle);
    if (result) {
        printf("Error: hardware SM3 init failed 0x%08X\n", result);
        return -1;
    }

    if (data_size > 0) {
        result = tcm2_hash_update(handle, (u8*)data, data_size);
        if (result) {
            printf("Error: hardware SM3 update failed 0x%08X\n", result);
            return -1;
        }
    }

    result = tcm2_hash_final(handle, NULL, 0, hash_output);
    if (result) {
        printf("Error: hardware SM3 final failed 0x%08X\n", result);
        return -1;
    }
#else
    struct sm3_context sw_ctx;
    se_sm3_init(&sw_ctx);

    if (data_size > 0) {
        se_sm3_update(&sw_ctx, data_size, (u8*)data);
    }

    se_sm3_final(&sw_ctx, hash_output);
#endif

    tcm_print_data("SM3 hash", hash_output, 32);

    return 0;
}

static int env_collect_and_hash(u8 *hash_output) {
    u8 buffer[4096];
    u32 data_size;
    int ret;

    if (!hash_output) return -1;

    ret = read_env_from_flash(buffer, sizeof(buffer), &data_size);
    if (ret != 0) {
        printf("Failed to read environment variables from Flash\n");
        return ret;
    }

    return tcm2_unified_memory_sm3_hash(buffer, data_size, hash_output);
}

int tcm2_env_auto_measure(void) {
    u8 current_hash[TCM2_HASH_SIZE];
    int ret;

    printf("Starting environment variable measurement...\n");

    ret = env_collect_and_hash(current_hash);
    if (ret != 0) {
        printf("Failed to collect and hash environment variables\n");
        return ret;
    }

    ret = tcm2_metric_store_or_verify(TCM2_METRIC_ENV_VARS, current_hash);
    if (ret == 0)
        printf("Env verify done\n");
    else
        printf("Env verify failed\n");

    return 0;
}

int tcm2_env_restore_cmd(int argc, char **argv) {
    u8 current_hash[TCM2_HASH_SIZE];
    int ret;

    tcm2_check_debug_flag(&argc, &argv);

    if (argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: tcm_env_restore\n");
        printf("Update environment variable baseline hash in TCM NV storage\n");
        printf("Examples:\n");
        printf("  tcm_env_restore : Update baseline with current environment\n");
        return 0;
    }

    if (argc != 1) {
        printf("Use --help or -h for more information\n");
        return -1;
    }

    ret = env_collect_and_hash(current_hash);
    if (ret != 0) {
        printf("Failed to collect and hash environment variables\n");
        return ret;
    }

    if (tcm2_metric_nv_exists(TCM2_METRIC_ENV_VARS)) {
        tcm2_metric_nv_delete(TCM2_METRIC_ENV_VARS);
    }

    ret = tcm2_metric_store_or_verify(TCM2_METRIC_ENV_VARS, current_hash);
    if (ret == 0) {
        printf("Environment baseline updated successfully\n");
        tcm_print_data("New baseline hash:", current_hash, TCM2_HASH_SIZE);
    } else {
        printf("Failed to store new baseline\n");
    }

    return ret;
}

int tcm2_env_status_cmd(int argc, char **argv) {
    u8 current_hash[TCM2_HASH_SIZE];
    u8 stored_hash[TCM2_HASH_SIZE];
    int ret;

    tcm2_check_debug_flag(&argc, &argv);

    if (argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: tcm_env_status\n");
        printf("Display current environment measurement status\n");
        printf("Examples:\n");
        printf("  tcm_env_status : Show environment measurement status\n");
        return 0;
    }

    if (argc != 1) {
        printf("Use --help or -h for more information\n");
        return -1;
    }

    ret = env_collect_and_hash(current_hash);
    if (ret != 0) {
        printf("Failed to collect and hash environment variables\n");
        return ret;
    }

    printf("=== TCM Environment Measurement Status ===\n");
    printf("NV Index: 0x%x\n", TCM2_METRIC_ENV_VARS);

    printf("Current hash: ");
    tcm_print_data("", current_hash, TCM2_HASH_SIZE);

    if (tcm2_metric_nv_exists(TCM2_METRIC_ENV_VARS)) {
        ret = tcm2_metric_nv_read(TCM2_METRIC_ENV_VARS, stored_hash);
        if (ret == 0) {
            printf("Baseline hash: ");
            tcm_print_data("", stored_hash, TCM2_HASH_SIZE);

            if (memcmp(current_hash, stored_hash, TCM2_HASH_SIZE) == 0) {
                printf("Status: VERIFIED - Environment matches baseline\n");
            } else {
                printf("Status: MISMATCH - Environment has changed!\n");
                printf("Run 'tcm_env_restore' to update baseline if change is expected\n");
            }
        } else {
            printf("Status: ERROR - Failed to read baseline\n");
        }
    } else {
        printf("Status: UNINITIALIZED - No baseline found\n");
        printf("Run 'tcm_env_restore' to establish baseline\n");
    }

    return 0;
}



