#ifndef tcm_log_H
#define tcm_log_H

#include <stdint.h>
#include <stddef.h>

// Set log level
void tcm_set_log_level(int level);

void tcm_debug(const char *format, ...);

void tcm_info(const char *format, ...);

void tcm_warn(const char *format, ...);

void tcm_err(const char *format, ...);

void tcm_print_bytes(const char *label, const uint8_t *data, size_t length);

void print_byte_string(const void *data, uint32_t len);

void tcm_print_data(const char *format, const void *data, uint32_t len);

void tcm_print_tcm2b(const char *label, const uint8_t *tcm2b_data);

void print_ascii_data(const uint8_t *data, uint32_t len);

#endif // tcm_log_H
