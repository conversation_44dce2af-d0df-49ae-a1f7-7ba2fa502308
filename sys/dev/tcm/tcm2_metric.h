#ifndef __TCM2_METRIC_H__
#define __TCM2_METRIC_H__

#include <tcm-common.h>

/* ========================================================================
 * TCM2 measurement system configuration and definitions
 * ======================================================================== */

// Hash size definitions
#define TCM2_HASH_SIZE 32

#define TCM2_METRIC_VMLINUZ      0x012ffffe
#define TCM2_METRIC_FILESYSTEM   0x012ffffd
#define TCM2_METRIC_ENV_VARS     0x012fff00

#define EMMC_BASE_PATH "/dev/fs/ext4@emmc0a/"

// eMMC critical files whitelist configuration
// Format: {filename, NV index}
#define EMMC_CRITICAL_FILES_CONFIG \
    {"vmlinuz",       0x012fff00}, \
    {"daemon",        0x012fff01}, \
    {"run.sh",        0x012fff02}, \
    {"printd",        0x012fff03}, \
    {"boot.mk",       0x012fff04}, \
    {"boot.cfg",      0x012fff05}, \
    {"config.ini",    0x012fff06}

#define MAX_EMMC_FILES 16
#define MAX_FILE_PATH_LEN 256
#define MAX_FILE_NAME_LEN 64

// Filesystem measurement policy configuration
// Enable this macro: measure critical files
// Disable this macro: measure entire filesystem image
#define MEASURE_CRITICAL_FILES 0
#define STOP_ON_MEASURE_FAILURE 0

// eMMC file configuration structure
typedef struct {
    char filename[MAX_FILE_NAME_LEN];
    u32 nv_index;  // NV space index
} emmc_file_config_t;

int tcm2_metric_nv_exists(u32 nv_index);
int tcm2_metric_nv_write(u32 nv_index, const u8 *hash_data);
int tcm2_metric_nv_read(u32 nv_index, u8 *hash_data);
int tcm2_metric_nv_delete(u32 nv_index);
int tcm2_metric_store_or_verify(u32 nv_index, const u8 *computed_hash);

// eMMC file test functions
int tcm2_test_emmc_files(void);                    // Test eMMC file access functionality
int tcm2_list_emmc_contents(void);                 // List eMMC directory contents
int tcm2_test_emmc_sm3_hash(void);                 // Test eMMC file SM3 hash calculation

int tcm2_measure_critical_files(int argc, char **argv);  // Critical file measurement

// Environment variable measurement functions
int tcm2_env_auto_measure(void);                       // Automatic environment variable measurement
int tcm2_env_restore_cmd(int argc, char **argv);       // Update environment variable baseline
int tcm2_env_status_cmd(int argc, char **argv);        // Display environment variable measurement status

// Kernel file type configuration macro
// Default kernel file type - can be changed at compile time or by modifying this line
// Valid values: "vmlinuz" or "vmlinux"
#define TCM2_KERNEL_FILE_TYPE "vmlinuz"

#endif /* __TCM2_METRIC_H__ */
