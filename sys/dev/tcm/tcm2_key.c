#include <TcmTypes.h>
#include <string.h>
#include <tcm-common.h>
#include <tcm-v2.h>

#include "tcm-utils.h"
#include "tcm2_log.h"
#include "tcm2_mailbox.h"
#include "tcm2_sm3.h"

// Handle authorization part TCMS_AUTH_COMMAND
void convert_TCMS_AUTH_COMMAND_to_buffer(uint8_t *buffer, TCMS_AUTH_COMMAND *auth) {
    u32 offset = 0;

    offset += 4;

    tcm_write_u32(buffer + offset, auth->sessionHandle);
    offset += 4;

    tcm_write_u16(buffer + offset, auth->nonce.size);
    offset += 2;
    if(auth->nonce.size > 0) {
        memcpy(buffer + offset, auth->nonce.buffer, auth->nonce.size);
        offset += auth->nonce.size;
    }

    tcm_write_u8(buffer + offset, auth->sessionAttributes);
    offset++;

    tcm_write_u16(buffer + offset, auth->hmac.size);
    offset += 2;
    if(auth->hmac.size != 0) {
        memcpy(buffer + offset, auth->hmac.buffer, auth->hmac.size);
        offset += auth->hmac.size;
    }

    auth->size = offset - 4;
    tcm_write_u32(buffer, auth->size);
}

// Handle byte order of private part
void convert_TCM2B_SENSITIVE_CREATE_to_buffer(uint8_t *buffer, TCM2B_SENSITIVE_CREATE *sensitive) {
    u32 offset = 0;

    offset += 2;

    tcm_write_u16(buffer + offset, sensitive->sensitive.userAuth.size);
    offset += 2;

    if(sensitive->sensitive.userAuth.size > 0) {
        memcpy(buffer + offset, sensitive->sensitive.userAuth.buffer, sensitive->sensitive.userAuth.size);
        offset += sensitive->sensitive.userAuth.size;
    }

    tcm_write_u16(buffer + offset, sensitive->sensitive.data.size);
    offset += 2;

    if(sensitive->sensitive.data.size > 0) {
        memcpy(buffer + offset, sensitive->sensitive.data.buffer, sensitive->sensitive.data.size);
        offset += sensitive->sensitive.data.size;
    }

    sensitive->size = offset - 2;
    tcm_write_u16(buffer, sensitive->size);
}

void convert_TCM2B_PUBLIC_to_buffer(uint8_t *buffer, TCM2B_PUBLIC *public) {
    size_t offset = 0;

    offset += 2;

    if(public->size != 0) {
        tcm_write_u16(buffer + offset, public->publicArea.type);
        offset += 2;

        tcm_write_u16(buffer + offset, public->publicArea.nameAlg);
        offset += 2;

        tcm_write_u32(buffer + offset, public->publicArea.objectAttributes);
        offset += 4;

        tcm_write_u16(buffer + offset, public->publicArea.authPolicy.size);
        offset += 2;
        if(public->publicArea.authPolicy.size > 0) {
            memcpy(buffer + offset, public->publicArea.authPolicy.buffer, public->publicArea.authPolicy.size);
            offset += public->publicArea.authPolicy.size;
        }

        switch(public->publicArea.type) {
            case TCM2_ALG_KEYEDHASH:
                tcm_write_u16(buffer + offset, public->publicArea.parameters.keyedHashDetail.scheme.scheme);
                offset += 2;

                tcm_write_u16(buffer + offset, public->publicArea.parameters.keyedHashDetail.scheme.details.hmac.hashAlg);
                offset += 2;
                break;

            case TCM2_ALG_SYMCIPHER:
                tcm_write_u16(buffer + offset, public->publicArea.parameters.symDetail.sym.algorithm);
                offset += 2;

                tcm_write_u16(buffer + offset, public->publicArea.parameters.symDetail.sym.keyBits.sym);
                offset += 2;

                tcm_write_u16(buffer + offset, public->publicArea.parameters.symDetail.sym.mode.sym);
                offset += 2;
                break;

            case TCM2_ALG_RSA:
                tcm_write_u16(buffer + offset, public->publicArea.parameters.rsaDetail.keyBits);
                offset += 2;

                tcm_write_u32(buffer + offset, public->publicArea.parameters.rsaDetail.exponent);
                offset += 4;
                break;

            case TCM2_ALG_ECC:
                tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.symmetric.algorithm);
                offset += 2;
                if(public->publicArea.parameters.eccDetail.symmetric.algorithm != TCM2_ALG_NULL) {
                    tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.symmetric.keyBits.sm4);
                    offset += 2;
                    tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.symmetric.mode.sm4);
                    offset += 2;
                }

                tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.scheme.scheme);
                offset += 2;
                if(public->publicArea.parameters.eccDetail.scheme.scheme != TCM2_ALG_NULL) {
                    tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.scheme.details.sm2.hashAlg);
                    offset += 2;
                }

                tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.curveID);
                offset += 2;

                tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.kdf.scheme);
                offset += 2;
                if(public->publicArea.parameters.eccDetail.kdf.scheme != TCM2_ALG_NULL) {
                    tcm_write_u16(buffer + offset, public->publicArea.parameters.eccDetail.kdf.details.kdf2.hashAlg);
                    offset += 2;
                }
                break;

            default:
                tcm_debug("Error: Unmatched type in TCM2B_PUBLIC");
                return;
        }

        // Handle unique field
        switch(public->publicArea.type) {
            case TCM2_ALG_KEYEDHASH:
                tcm_write_u16(buffer + offset, public->publicArea.unique.keyedHash.size);
                offset += 2;

                if(public->publicArea.unique.keyedHash.size > 0) {
                    memcpy(buffer + offset, public->publicArea.unique.keyedHash.buffer, public->publicArea.unique.keyedHash.size);
                    offset += public->publicArea.unique.keyedHash.size;
                }
                break;

            case TCM2_ALG_SYMCIPHER:
                tcm_write_u16(buffer + offset, public->publicArea.unique.sym.size);
                offset += 2;

                if(public->publicArea.unique.sym.size > 0) {
                    memcpy(buffer + offset, public->publicArea.unique.sym.buffer, public->publicArea.unique.sym.size);
                    offset += public->publicArea.unique.sym.size;
                }
                break;

            case TCM2_ALG_RSA:
                tcm_write_u16(buffer + offset, public->publicArea.unique.rsa.size);
                offset += 2;

                if(public->publicArea.unique.rsa.size > 0) {
                    memcpy(buffer + offset, public->publicArea.unique.rsa.buffer, public->publicArea.unique.rsa.size);
                    offset += public->publicArea.unique.rsa.size;
                }
                break;

            case TCM2_ALG_ECC:
                tcm_write_u16(buffer + offset, public->publicArea.unique.ecc.x.size);
                offset += 2;

                if(public->publicArea.unique.ecc.x.size > 0) {
                    memcpy(buffer + offset, public->publicArea.unique.ecc.x.buffer, public->publicArea.unique.ecc.x.size);
                    offset += public->publicArea.unique.ecc.x.size;
                }

                tcm_write_u16(buffer + offset, public->publicArea.unique.ecc.y.size);
                offset += 2;

                if(public->publicArea.unique.ecc.y.size > 0) {
                    memcpy(buffer + offset, public->publicArea.unique.ecc.y.buffer, public->publicArea.unique.ecc.y.size);
                    offset += public->publicArea.unique.ecc.y.size;
                }
                break;

            default:
                tcm_debug("Error: Unmatched type in TCM2B_PUBLIC");
                return;
        }

        public->size = offset - 2;
        tcm_write_u16(buffer, public->size);
    }
}

u32 tcm2_createPrimary(u32 parent_handle, TCMS_AUTH_COMMAND auth, TCM2B_SENSITIVE_CREATE sensitive, TCM2B_PUBLIC public, u32 *out_handle, u8 *pub_data, u32 *pub_size) {
    tcm_debug("Entering tcm2_CreatePrimary function");

    if(out_handle == NULL || pub_data == NULL || pub_size == NULL) {
        tcm_err("%s:%d Null pointer passed to tcm2_CreatePrimary, code: 0x%08X", __func__, __LINE__, TCM2_RC_FAILURE);
        return TCM2_RC_FAILURE;
    }

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(0),
        tcm_u32(TCM2_CC_CreatePrimary),

        tcm_u32(parent_handle),
    };

    u32 offset = 14;
    convert_TCMS_AUTH_COMMAND_to_buffer(command + offset, &auth);
    offset += auth.size + 4;

    convert_TCM2B_SENSITIVE_CREATE_to_buffer(command + offset, &sensitive);
    offset += sensitive.size + 2;

    convert_TCM2B_PUBLIC_to_buffer(command + offset, &public);
    offset += public.size + 2;

    tcm_write_u16(command + offset, 0);
    offset += 2;

    tcm_write_u32(command + offset, 0);
    offset += 4;

    tcm_write_u32(command + 2, offset);

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("%s:%d tcm_sendrecv_command failed, return code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u32 offset_res = 10;

    if(resp_len < offset_res + sizeof(u32) + sizeof(u16)) {
        tcm_err("%s:%d Response too short, code: 0x%08X", __func__, __LINE__, TCM2_RC_FAILURE);
        return TCM2_RC_FAILURE;
    }

    u32 newHandle = tcm_read_u32(response + offset_res);
    *out_handle = newHandle;
    tcm_debug("New handle created: 0x%08X", *out_handle);
    offset_res += 4;

    u16 pubAreaSize = tcm_read_u32(response + offset_res);
    tcm_debug("Public area size in response: %u bytes", pubAreaSize);
    offset_res += 2;

    int res = unpack_byte_string(response, resp_len, "s", offset_res, pub_data, pubAreaSize);
    if(res != 0)
        return -1;
    *pub_size = pubAreaSize;

    // Print public key data
    /*     tcm_debug("Public key data:");
        tcm_print_data(pub_data, pubAreaSize); */

    tcm_debug("tcm2_createPrimary created successfully");
    return TCM2_RC_SUCCESS;
}

u32 tcm2_create(u32 parent_handle, TCMS_AUTH_COMMAND auth, TCM2B_SENSITIVE_CREATE sensitive, TCM2B_PUBLIC public, TCM2B_PRIVATE *out_private, TCM2B_PUBLIC *out_public) {
    tcm_debug("Entering tcm2_Create function");

    if(out_private == NULL || out_public == NULL) {
        tcm_err("%s:%d Null pointer passed to tcm2_Create, code: 0x%08X", __func__, __LINE__, TCM2_RC_FAILURE);
        return TCM2_RC_FAILURE;
    }

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(0),
        tcm_u32(TCM2_CC_Create),

        tcm_u32(parent_handle),
    };
    u32 offset = 14;

    convert_TCMS_AUTH_COMMAND_to_buffer(command + offset, &auth);
    offset += auth.size + 4;

    convert_TCM2B_SENSITIVE_CREATE_to_buffer(command + offset, &sensitive);
    offset += sensitive.size + 2;

    convert_TCM2B_PUBLIC_to_buffer(command + offset, &public);
    offset += public.size + 2;

    tcm_write_u16(command + offset, 0);
    offset += 2;

    tcm_write_u32(command + offset, 0);
    offset += 4;

    tcm_write_u32(command + 2, offset);

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("%s:%d tcm_sendrecv_command failed, return code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u32 offset_res = 14;

    u16 privSize = tcm_read_u16(response + offset_res);
    out_private->size = privSize;
    offset_res += 2;
    memcpy(out_private->buffer, response + offset_res, privSize);
    offset_res += privSize;

    u16 pubSize = tcm_read_u16(response + offset_res);
    out_public->size = pubSize;
    offset_res += 2;
    memcpy(&out_public->publicArea, response + offset_res, pubSize);

    tcm_debug("Private key size: %u\n", privSize);
    tcm_debug("After create - Private key size: %u\n", out_private->size);
    tcm_debug("tcm2_create completed successfully");
    return TCM2_RC_SUCCESS;
}

u32 tcm2_load(u32 parent_handle, TCMS_AUTH_COMMAND auth, TCM2B_PRIVATE *inPrivate, TCM2B_PUBLIC *inPublic, u32 *out_handle) {
    if(inPrivate == NULL || inPublic == NULL || out_handle == NULL) {
        tcm_err("%s:%d Null pointer passed to tcm2_load, code: 0x%08X", __func__, __LINE__, TCM2_RC_FAILURE);
        return TCM2_RC_FAILURE;
    }

    // Print size of inPrivate and inPublic
    tcm_debug("Size of TCM2B_PRIVATE: %zu\n", sizeof(TCM2B_PRIVATE));
    tcm_debug("inPrivate size: %u", inPrivate->size);
    tcm_debug("inPublic size: %u", inPublic->size);

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(0),
        tcm_u32(TCM2_CC_Load),

        tcm_u32(parent_handle),
    };
    u32 offset = 14;

    convert_TCMS_AUTH_COMMAND_to_buffer(command + offset, &auth);
    offset += auth.size + 4;

    tcm_write_u16(command + offset, inPrivate->size);
    offset += 2;
    memcpy(command + offset, inPrivate->buffer, inPrivate->size);
    offset += inPrivate->size;

    tcm_write_u16(command + offset, inPublic->size);
    offset += 2;
    memcpy(command + offset, &inPublic->publicArea, inPublic->size);
    offset += inPublic->size;

    tcm_write_u32(command + 2, offset);

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != TCM2_RC_SUCCESS) {
        tcm_err("%s:%d tcm_sendrecv_command failed, return code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u32 resp_offset = 10;

    *out_handle = tcm_read_u32(response + resp_offset);
    tcm_debug("Loaded object handle: 0x%08X", *out_handle);
    resp_offset += 4;
    return TCM2_RC_SUCCESS;
}

void tcm2_createPrimary_init(TCM_CC command_code, TCMS_AUTH_COMMAND *auth, TCM2B_SENSITIVE_CREATE *sen, TCM2B_PUBLIC *pub) {
    // auth init
    auth->sessionHandle = TCM2_RS_PW;
    auth->nonce.size = 0;
    auth->sessionAttributes = 0x00;
    auth->hmac.size = 0;

    // sensitive init, but can be changed
    sen->size = 4;
    sen->sensitive.userAuth.size = 0;
    sen->sensitive.data.size = 0;

    switch(command_code) {
        case TCM2_CC_HMAC:
            // public->size will be changed by Convert_TCM2B_PUBLIC_to_buffer
            pub->size = 16;
            pub->publicArea.type = TCM2_ALG_KEYEDHASH;
            pub->publicArea.nameAlg = TCM2_ALG_SM3_256;
            pub->publicArea.objectAttributes = TCMA_OBJECT_SIGN |
                                               TCMA_OBJECT_fixedTCM |
                                               TCMA_OBJECT_fixedParent |
                                               TCMA_OBJECT_sensitiveDataOrigin |
                                               TCMA_OBJECT_userWithAuth;
            pub->publicArea.authPolicy.size = 0;
            pub->publicArea.parameters.keyedHashDetail.scheme.scheme = TCM2_ALG_HMAC;
            pub->publicArea.parameters.keyedHashDetail.scheme.details.hmac.hashAlg = TCM2_ALG_SM3_256;
            pub->publicArea.unique.keyedHash.size = 0;
            break;

        case TCM2_CC_Sign:
            sen->size = 1;
            sen->sensitive.userAuth.size = 3;
            sen->sensitive.userAuth.buffer[0] = 0x73;  // 's'
            sen->sensitive.userAuth.buffer[1] = 0x74;  // 't'
            sen->sensitive.userAuth.buffer[2] = 0x6f;  // 'o'
            sen->sensitive.data.size = 0;

            pub->size = 1;
            pub->publicArea.type = TCM2_ALG_ECC;
            pub->publicArea.nameAlg = TCM2_ALG_SM3_256;
            pub->publicArea.objectAttributes = 0x00030072;
            pub->publicArea.authPolicy.size = 0;

            pub->publicArea.parameters.eccDetail.symmetric.algorithm = TCM2_ALG_SM4;
            pub->publicArea.parameters.eccDetail.symmetric.keyBits.sym = 128;
            pub->publicArea.parameters.eccDetail.symmetric.mode.sym = TCM2_ALG_CFB;

            pub->publicArea.parameters.eccDetail.scheme.scheme = TCM2_ALG_NULL;
            pub->publicArea.parameters.eccDetail.curveID = TCM2_ECC_SM2_P256;
            pub->publicArea.parameters.eccDetail.kdf.scheme = TCM2_ALG_NULL;

            pub->publicArea.unique.ecc.x.size = 0;
            pub->publicArea.unique.ecc.y.size = 0;
            break;

        case TCM2_CC_ECC_Decrypt:
            pub->size = 1;
            pub->publicArea.type = TCM2_ALG_ECC;
            pub->publicArea.nameAlg = TCM2_ALG_SM3_256;
            pub->publicArea.objectAttributes = TCMA_OBJECT_DECRYPT |
                                               TCMA_OBJECT_USERWITHAUTH |
                                               TCMA_OBJECT_SENSITIVEDATAORIGIN;
            pub->publicArea.authPolicy.size = 0;
            pub->publicArea.parameters.eccDetail.symmetric.algorithm = TCM2_ALG_NULL;
            pub->publicArea.parameters.eccDetail.scheme.scheme = TCM2_ALG_SM2;
            pub->publicArea.parameters.eccDetail.scheme.details.sm2.hashAlg = TCM2_ALG_SM3_256;
            pub->publicArea.parameters.eccDetail.curveID = TCM2_ECC_SM2_P256;
            pub->publicArea.parameters.eccDetail.kdf.scheme = TCM2_ALG_NULL;
            pub->publicArea.unique.ecc.x.size = 0;
            pub->publicArea.unique.ecc.y.size = 0;
            break;

        case TCM2_CC_EncryptDecrypt2:
            pub->size = 1;
            pub->publicArea.type = TCM2_ALG_SYMCIPHER;
            pub->publicArea.nameAlg = TCM2_ALG_SM3_256;
            pub->publicArea.objectAttributes = TCMA_OBJECT_DECRYPT |
                                               TCMA_OBJECT_SIGN_ENCRYPT |
                                               TCMA_OBJECT_USERWITHAUTH |
                                               TCMA_OBJECT_SENSITIVEDATAORIGIN;
            pub->publicArea.authPolicy.size = 0;
            pub->publicArea.parameters.symDetail.sym.algorithm = TCM2_ALG_SM4;
            pub->publicArea.parameters.symDetail.sym.keyBits.sym = 128;
            pub->publicArea.parameters.symDetail.sym.mode.sym = TCM2_ALG_NULL;
            break;

        default:
            tcm_err("%s:%d Unsupported command code: 0x%08X", __func__, __LINE__, command_code);
            break;
    }
}

void tcm2_create_init(TCM_CC command_code, TCMS_AUTH_COMMAND *auth, TCM2B_SENSITIVE_CREATE *sen, TCM2B_PUBLIC *pub) {
    // just for TCM2_CC_Sign, because createprimary is used for all command_code
    // you can usr create && load, because it is more standard
    switch(command_code) {
        case TCM2_CC_Sign:
            auth->size = 12;
            auth->sessionHandle = TCM2_RS_PW;
            auth->nonce.size = 0;
            auth->sessionAttributes = 0x00;
            auth->hmac.size = 3;
            auth->hmac.buffer[0] = 0x73;
            auth->hmac.buffer[1] = 0x74;
            auth->hmac.buffer[2] = 0x6F;

            sen->size = 1;
            sen->sensitive.userAuth.size = 3;
            sen->sensitive.userAuth.buffer[0] = 0x73;
            sen->sensitive.userAuth.buffer[1] = 0x69;
            sen->sensitive.userAuth.buffer[2] = 0x67;
            sen->sensitive.data.size = 0;

            pub->size = 1;
            pub->publicArea.type = TCM2_ALG_ECC;
            pub->publicArea.nameAlg = TCM2_ALG_SM3_256;
            pub->publicArea.objectAttributes = 0x00040472;
            pub->publicArea.authPolicy.size = 0;
            pub->publicArea.parameters.eccDetail.symmetric.algorithm = TCM2_ALG_NULL;
            pub->publicArea.parameters.eccDetail.symmetric.keyBits.sym = 128;
            pub->publicArea.parameters.eccDetail.symmetric.mode.sym = TCM2_ALG_CFB;  // Hardware only supports CFB mode
            pub->publicArea.parameters.eccDetail.scheme.scheme = TCM2_ALG_NULL;
            pub->publicArea.parameters.eccDetail.scheme.details.ecdsa.hashAlg = TCM2_ALG_SM3_256;
            pub->publicArea.parameters.eccDetail.curveID = TCM2_ECC_SM2_P256;
            pub->publicArea.parameters.eccDetail.kdf.scheme = TCM2_ALG_NULL;
            pub->publicArea.unique.ecc.x.size = 0;
            pub->publicArea.unique.ecc.y.size = 0;
            break;
        default:
            tcm_err("%s:%d Unsupported command code: 0x%08X", __func__, __LINE__, command_code);
            break;
    }
}

TCM2B_PRIVATE out_private = {0};
TCM2B_PUBLIC out_public = {0};
TCMS_AUTH_COMMAND auth;

int tcm2_createprimary_cmd(int argc, char *argv[]) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <type>\n", argv[0]);
        printf("Create a primary key in the TCM\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <type>         : Type of key to create\n");
        printf("                   hmac - Create key for HMAC operations\n");
        printf("                   sign - Create SM2 key for signing\n");
        printf("                   sm2  - Create SM2 key for encryption/decryption\n");
        printf("                   sm4  - Create SM4 key for symmetric encryption/decryption\n");
        printf("Examples:\n");
        printf("  %s hmac        : Create a primary key for HMAC operations\n", argv[0]);
        printf("  %s sm2         : Create a primary SM2 key for encryption/decryption\n", argv[0]);
        printf("Note: The created key handle will be displayed upon successful creation\n");
        return 0;
    }
    
    if(argc < 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    TCM_CC command_code;
    if(strcmp(argv[1], "hmac") == 0)
        command_code = TCM2_CC_HMAC;
    else if(strcmp(argv[1], "sign") == 0)
        command_code = TCM2_CC_Sign;
    else if(strcmp(argv[1], "sm2") == 0)
        command_code = TCM2_CC_ECC_Decrypt;
    else if(strcmp(argv[1], "sm4") == 0)
        command_code = TCM2_CC_EncryptDecrypt2;
    else {
        printf("Invalid type. Must be one of: hmac, sign, sm2, sm4\n");
        return -1;
    }

    u32 parent_handle;
    if(command_code == TCM2_CC_Sign)
        parent_handle = TCM2_RH_PLATFORM;
    else
        parent_handle = TCM2_RH_NULL;

    TCM2B_SENSITIVE_CREATE sen;
    TCM2B_PUBLIC pub;

    tcm2_createPrimary_init(command_code, &auth, &sen, &pub);

    u32 out_handle = 0;
    u8 out_pub[1024];
    u32 out_pubsize = sizeof(out_pub);

    u32 result = tcm2_createPrimary(parent_handle, auth, sen, pub, &out_handle, out_pub, &out_pubsize);
    if(result != 0) {
        tcm_err("Failed to create primary key. Error code: 0x%08X\n", result);
    }

    printf("Create primary key handle:%08x\n", out_handle);
    return 0;
}

int tcm2_create_cmd(int argc, char *argv[]) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <parent_handle>\n", argv[0]);
        printf("Create a key object under the specified parent key\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <parent_handle>: Handle of the parent key (in hex format, e.g. 0x80000000)\n");
        printf("Examples:\n");
        printf("  %s 0x80000000  : Create a key under the primary key with handle 0x80000000\n", argv[0]);
        printf("Note: This command creates a signing key by default\n");
        return 0;
    }
    
    if(argc < 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return 1;
    }

    u32 parent_handle;
    if(sscanf(argv[1], "0x%x", &parent_handle) != 1) {
        tcm_err("%s:%d Invalid parent handle format. Use hexadecimal format (e.g., 0x80000000), code: 0x%08X", __func__, __LINE__, TCM2_RC_VALUE);
        return 1;
    }

    TCM2B_SENSITIVE_CREATE sen;
    TCM2B_PUBLIC pub;
    tcm2_create_init(TCM2_CC_Sign, &auth, &sen, &pub);
    u32 result = tcm2_create(parent_handle, auth, sen, pub, &out_private, &out_public);
    if(result != TCM2_RC_SUCCESS) {
        tcm_err("%s:%d Failed to create key, error code: 0x%08X", __func__, __LINE__, result);
        return result;
    }

    printf("Create parent_handle:%08x\n", parent_handle);
    return 0;
}

int tcm2_load_cmd(int argc, char *argv[]) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <parent_handle>\n", argv[0]);
        printf("Load a key object into the TCM\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <parent_handle>: Handle of the parent key (in hex format, e.g. 0x80000000)\n");
        printf("Examples:\n");
        printf("  %s 0x80000000  : Load a key under the primary key with handle 0x80000000\n", argv[0]);
        printf("Note: This command loads the key created by the most recent tcm2_create command\n");
        printf("      and returns a handle to the loaded key\n");
        return 0;
    }
    
    if(argc < 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return 1;
    }

    u32 parent_handle;
    if(sscanf(argv[1], "0x%x", &parent_handle) != 1) {
        tcm_err("%s:%d Invalid parent handle format. Use hexadecimal format (e.g., 0x80000000), code: 0x%08X", __func__, __LINE__, TCM2_RC_VALUE);
        return 1;
    }

    u32 out_handle = 0;
    u32 ret = tcm2_load(parent_handle, auth, &out_private, &out_public, &out_handle);
    if(ret != TCM2_RC_SUCCESS) {
        tcm_err("%s:%d Failed to load key, error code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    printf("load parent_handle:%08x\n", out_handle);

    return 0;
}
