#include <TcmTypes.h>
#include <string.h>
#include <tcm-common.h>
#include <tcm-v2.h>

#include "tcm-utils.h"
#include "tcm2_internal.h"
#include "tcm2_mailbox.h"

u32 tcm2_ecc_encrypt(const u32 key_handle, const u8 *plaintext, u16 plaintext_len, TCM2B_ECC_POINT *c1, TCM2B_MAX_BUFFER *c2, TCM2B_DIGEST *c3) {
    tcm_debug("plaintext len: %u", plaintext_len);
    tcm_debug("key_handle: 0x%08X", key_handle);

    u8 command[COMMAND_BUFFER_SIZE] = {
        // TCM_HEADER
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(20 + plaintext_len),
        tcm_u32(TCM2_CC_ECC_Encrypt),

        // handle
        tcm_u32(key_handle),
    };

    u32 data_offset = 14;

    tcm_write_u16(command + data_offset, plaintext_len);
    data_offset += 2;
    memcpy(command + data_offset, plaintext, plaintext_len);
    data_offset += plaintext_len;

    tcm_write_u16(command + data_offset, TCM2_ALG_KDF2);
    tcm_write_u16(command + data_offset + 2, TCM2_ALG_SM3_256);


    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("%s:%d tcm_sendrecv_command failed, return code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u16 offset = 10;

    u16 c1_size = tcm_read_u16(response + offset);
    (*c1).size = c1_size;
    tcm_debug("c1 size: %u", (*c1).size);
    offset += sizeof(u16);

    u16 x_size = tcm_read_u16(response + offset);
    offset += sizeof(u16);
    memcpy((*c1).point.x.buffer, response + offset, x_size);
    (*c1).point.x.size = x_size;
    offset += x_size;

    u16 y_size = tcm_read_u16(response + offset);
    offset += sizeof(u16);
    memcpy((*c1).point.y.buffer, response + offset, y_size);
    (*c1).point.y.size = y_size;
    offset += y_size;

    u16 c2_size = tcm_read_u16(response + offset);
    offset += sizeof(u16);
    memcpy((*c2).buffer, response + offset, c2_size);
    (*c2).size = c2_size;
    offset += c2_size;

    u16 c3_size = tcm_read_u16(response + offset);
    offset += sizeof(u16);
    memcpy((*c3).buffer, response + offset, c3_size);
    (*c3).size = c3_size;

    return TCM2_RC_SUCCESS;
}

u32 tcm2_ecc_decrypt(const u32 key_handle, const TCM2B_ECC_POINT c1, const TCM2B_MAX_BUFFER c2, const TCM2B_DIGEST c3, TCM2B_MAX_BUFFER *plainText) {

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),     // tag
        tcm_u32(0),                    // commandSize（later change）
        tcm_u32(TCM2_CC_ECC_Decrypt),  // commandCode

        // handle
        tcm_u32(key_handle),
        // Authorization Area（TCMS_AUTH_COMMAND）
        tcm_u32(9),           // authorizationAreaSize
        tcm_u32(TCM2_RS_PW),  // sessionHandle
        tcm_u16(0),           // nonce.size
        0x00,                 // sessionAttributes
        tcm_u16(0),           // hmac.size
    };

    u16 offest = 27;

    tcm_write_u16(command + offest, c1.size);
    offest += sizeof(u16);

    tcm_write_u16(command + offest, c1.point.x.size);
    offest += sizeof(u16);

    memcpy(command + offest, c1.point.x.buffer, c1.point.x.size);
    offest += c1.point.x.size;

    tcm_write_u16(command + offest, c1.point.y.size);
    offest += sizeof(u16);

    memcpy(command + offest, c1.point.y.buffer, c1.point.y.size);
    offest += c1.point.y.size;

    tcm_write_u16(command + offest, c2.size);
    offest += sizeof(u16);
    memcpy(command + offest, c2.buffer, c2.size);
    offest += c2.size;

    tcm_write_u16(command + offest, c3.size);
    offest += sizeof(u16);
    memcpy(command + offest, c3.buffer, c3.size);
    offest += c3.size;

    tcm_write_u16(command + offest, TCM2_ALG_KDF2);
    tcm_write_u16(command + offest + 2, TCM2_ALG_SM3_256);
    offest += 4;

    tcm_write_u32(command + 2, (u32)offest);

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);
    if(ret != 0) {
        tcm_err("%s:%d tcm_sendrecv_command failed, return code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u16 offset_resp = 14;
    u16 plainText_size = tcm_read_u16(response + offset_resp);
    offset_resp += sizeof(u16);
    memcpy((*plainText).buffer, response + offset_resp, plainText_size);
    (*plainText).size = plainText_size;

    return TCM2_RC_SUCCESS;
}

u8 cipherdata[1024] = {0};
u16 cipherdata_len = sizeof(cipherdata);

int tcm2_sm2encryptdecrypt_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    
    // Default to both encrypt and decrypt
    int mode = 0; // 0: both encrypt and decrypt, 1: encrypt only, 2: decrypt only
    
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-enc") == 0 || strcmp(argv[i], "--encrypt") == 0) {
            mode = 1;
            for (int j = i; j < argc - 1; j++)
                argv[j] = argv[j + 1];
            argc--;
            i--;
        } else if (strcmp(argv[i], "-dec") == 0 || strcmp(argv[i], "--decrypt") == 0) {
            mode = 2;
            for (int j = i; j < argc - 1; j++)
                argv[j] = argv[j + 1];
            argc--;
            i--;
        }
    }
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s [-enc|-dec] <key_handle> [message|memory_address size]\n", argv[0]);
        printf("Encrypt and decrypt data using SM2 algorithm\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  -enc, --encrypt: Only perform encryption\n");
        printf("  -dec, --decrypt: Only perform decryption (requires previous encryption)\n");
        printf("  <key_handle>   : Handle of the SM2 key to use (in hex format, e.g. 0x80000000)\n");
        printf("  [message]      : Optional - Message to encrypt/decrypt (in quotes)\n");
        printf("                   If not provided, a default test message will be used\n");
        printf("  [memory_address]: Optional - Memory address to read data from (in hex format)\n");
        printf("  [size]         : Size of data to read from memory address (in bytes)\n");
        printf("Examples:\n");
        printf("  %s 0x80000000                  : Encrypt and decrypt default test message\n", argv[0]);
        printf("  %s -enc 0x80000000 \"Hello World\" : Only encrypt 'Hello World'\n", argv[0]);
        printf("  %s -dec 0x80000000              : Only decrypt previously encrypted data\n", argv[0]);
        printf("  %s 0x80000000 0x900000000b02cc00 32 : Read 32 bytes from memory and encrypt/decrypt; Max size 1024\n", argv[0]);
        printf("Note: Message will be padded to 16-byte alignment using PKCS7\n");
        return 0;
    }
    
    if(argc < 2 || argc > 4) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return 1;
    }

    u32 handle;
    if(sscanf(argv[1], "0x%x", &handle) != 1) {
        tcm_err("%s:%d Invalid handle format. Use hexadecimal format (e.g., 0x80000000), code: 0x%08X", __func__, __LINE__, TCM2_RC_VALUE);
        return 1;
    }

    u8 *input_data;
    u32 input_len;
    u8 buffer[1024] = {0};
    
    if (argc == 2) {
        if (mode == 2 && cipherdata_len > 0) {
            // Reserved for future use
        } else {
            input_data = (u8 *)"Loongson Test Message: hello world";
            input_len = strlen((char *)input_data);
        }
    } else if(argc == 3) {
        input_data = (u8 *)argv[2];
        input_len = strlen((char *)input_data);
    } else if(argc == 4) {
        unsigned long long addr = strtoull(argv[2], NULL, 0);
        input_len = strtoul(argv[3], NULL, 0);
        if(input_len > sizeof(buffer)) {
            tcm_err("%s:%d Input data too large, maximum size is %d bytes", __func__, __LINE__, sizeof(buffer));
            return -1;
        }
        memcpy(buffer, (void *)addr, input_len);
        input_data = buffer;
    }

    u32 ret = 0;
    TCM2B_ECC_POINT c1;
    TCM2B_MAX_BUFFER c2;
    TCM2B_DIGEST c3;

    if (mode == 0 || mode == 1) {
        ret = tcm2_ecc_encrypt(handle, input_data, input_len, &c1, &c2, &c3);
        if(ret != TCM2_RC_SUCCESS) {
            tcm_err("%s:%d SM2 encryption failed. Error code: 0x%08X", __func__, __LINE__, ret);
            return ret;
        }

        cipherdata_len = c2.size;
        memcpy(cipherdata, c2.buffer, cipherdata_len);
        
        tcm_print_data("[DEBUG] Original input", input_data, input_len);
        tcm_print_data("[DEBUG] Encrypted data", c2.buffer, c2.size);
        
        if (mode == 1) 
            return 0;
    }

    if (mode == 0 || mode == 2) {
        if (mode == 2) {
            c2.size = cipherdata_len;
            memcpy(c2.buffer, cipherdata, cipherdata_len);
        }
        
        TCM2B_MAX_BUFFER output_data;
        ret = tcm2_ecc_decrypt(handle, c1, c2, c3, &output_data);
        if(ret != TCM2_RC_SUCCESS) {
            tcm_err("%s:%d SM2 decryption failed. Error code: 0x%08X", __func__, __LINE__, ret);
            return ret;
        }

        if (mode == 2) {
            tcm_print_data("[DEBUG] Encrypted data", c2.buffer, c2.size);
        }
        tcm_print_data("[DEBUG] Decrypted data", output_data.buffer, output_data.size);
    }

    return 0;
}
