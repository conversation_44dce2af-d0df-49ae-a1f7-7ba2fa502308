#include <TcmTypes.h>
#include <string.h>
#include <tcm-common.h>
#include <tcm-v2.h>

#include "tcm-utils.h"
#include "tcm2_internal.h"
#include "tcm2_mailbox.h"

u32 tcm2_sign(u32 key_handle, TCM2B_DIGEST digest, TCMT_SIGNATURE *signature) {
    tcm_debug("key_handle: 0x%08X", key_handle);

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(0),
        tcm_u32(TCM2_CC_Sign),

        tcm_u32(key_handle),

        tcm_u32(12),
        tcm_u32(TCM2_RS_PW),
        tcm_u16(0),
        0x00,
        tcm_u16(3),
        0x73, 0x69, 0x67,

        tcm_u16(digest.size),
    };

    u32 offset = 32;
    memcpy(command + offset, digest.buffer, digest.size);
    offset += digest.size;

    // TCMT_SIG_SCHEME
    tcm_write_u16(command + offset, TCM2_ALG_SM2);
    offset += 2;
    tcm_write_u16(command + offset, TCM2_ALG_SM3_256);
    offset += 2;
    /* tcm_write_u16(command + offset, TCM2_ALG_NULL);
    offset += 2; */

    // TCMT_TK_HASHCHECK ticket option: use empty ticket since key is not restricted
    tcm_write_u16(command + offset, TCM_ST_HASHCHECK);
    offset += 2;
    tcm_write_u32(command + offset, TCM2_RH_NULL);
    offset += 4;
    tcm_write_u16(command + offset, 0);
    offset += 2;

    tcm_write_u32(command + 2, offset);
    tcm_debug("Command length: %u", offset);

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);
    if(ret != 0) {
        tcm_err("%s:%d tcm_sendrecv_command failed, return code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u16 resp_offset = 14;
    // Get scheme.scheme
    signature->sigAlg = tcm_read_u16(response + resp_offset);
    resp_offset += 2;
    // scheme.details ALG_SM2  TCMS_SCHEME_HASH
    signature->signature.sm2.hash = tcm_read_u16(response + resp_offset);
    tcm_debug("sigalg: 0x%08X , hash: 0x%08X", signature->sigAlg, signature->signature.sm2.hash);
    resp_offset += 2;

    // r
    u16 r_size = tcm_read_u16(response + resp_offset);
    resp_offset += 2;
    memcpy(signature->signature.sm2.signatureR.buffer, response + resp_offset, r_size);
    signature->signature.sm2.signatureR.size = r_size;
    resp_offset += r_size;

    // s
    u16 s_size = tcm_read_u16(response + resp_offset);
    resp_offset += 2;
    memcpy(signature->signature.sm2.signatureS.buffer, response + resp_offset, s_size);
    signature->signature.sm2.signatureS.size = s_size;

    return TCM2_RC_SUCCESS;
}

u32 tcm2_verify(u32 key_handle, TCM2B_DIGEST digest, const TCMT_SIGNATURE *signature, TCMT_TK_VERIFIED *validation) {

    tcm_debug("key_handle: 0x%08X", key_handle);
    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(0),
        tcm_u32(TCM2_CC_VerifySignature),
        tcm_u32(key_handle),
        tcm_u16(digest.size),
    };

    u32 offset = 16;
    memcpy(command + offset, digest.buffer, digest.size);
    offset += digest.size;

    // Write TCMT_SIGNATURE
    tcm_write_u16(command + offset, TCM2_ALG_SM2);  // sigAlg
    offset += 2;
    tcm_write_u16(command + offset, TCM2_ALG_SM3_256);  // hash
    offset += 2;
    /* tcm_write_u16(command + offset, TCM2_ALG_NULL);
    offset += 2; */

    // Write R
    tcm_write_u16(command + offset, signature->signature.sm2.signatureR.size);
    offset += 2;
    memcpy(command + offset, signature->signature.sm2.signatureR.buffer, signature->signature.sm2.signatureR.size);
    offset += signature->signature.sm2.signatureR.size;

    // Write S
    tcm_write_u16(command + offset, signature->signature.sm2.signatureS.size);
    offset += 2;
    memcpy(command + offset, signature->signature.sm2.signatureS.buffer, signature->signature.sm2.signatureS.size);
    offset += signature->signature.sm2.signatureS.size;

    tcm_write_u32(command + 2, offset);

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("%s:%d tcm_sendrecv_command failed, return code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    return TCM2_RC_SUCCESS;
}

int tcm2_sigver_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    u32 result;
    u8 public_share_data[1024];
    u32 public_share_data_len = sizeof(public_share_data);

    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s [-s|-v] <key_handle> [memory_address size]\n", argv[0]);
        printf("Sign and verify a message using SM2 algorithm\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  -s, --sign     : Only perform signing operation\n");
        printf("  -v, --verify   : Only perform verification operation (requires previous signing)\n");
        printf("  <key_handle>   : Handle of the SM2 key to use for signing/verification (in hex format, e.g. 0x80000000)\n");
        printf("  [memory_address]: Optional - Memory address to read data from (in hex format)\n");
        printf("  [size]         : Size of data to read from memory address (in bytes, must be exactly 32 bytes)\n");
        printf("Examples:\n");
        printf("  %s 0x80000000  : Sign and verify using default test data\n", argv[0]);
        printf("  %s -s 0x80000000 : Only sign using default test data\n", argv[0]);
        printf("  %s -v 0x80000000 : Only verify previously signed data\n", argv[0]);
        printf("  %s 0x80000000 0x900000000b02cc00 32 : Read 32 bytes from memory address and sign/verify\n", argv[0]);
        printf("Note: The size parameter must be exactly 32 bytes for SM2 signature\n");
        return 0;
    }

    if(argc < 2 || argc > 5) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return 1;
    }

    int sign_only = 0;
    int verify_only = 0;
    int arg_offset = 0;

    if (strcmp(argv[1], "-s") == 0 || strcmp(argv[1], "--sign") == 0) {
        sign_only = 1;
        verify_only = 0;
        arg_offset = 1;
    } else if (strcmp(argv[1], "-v") == 0 || strcmp(argv[1], "--verify") == 0) {
        sign_only = 0;
        verify_only = 1;
        arg_offset = 1;
    }

    if (argc - arg_offset < 2) {
        tcm_err("Missing key handle parameter\n");
        tcm_err("Use --help or -h for more information\n");
        return 1;
    }

    u32 sm2_key_handle;
    if(sscanf(argv[1 + arg_offset], "0x%x", &sm2_key_handle) != 1) {
        tcm_err("%s:%d Invalid parent handle format. Use hexadecimal format (e.g., 0x80000000), code: 0x%08X", __func__, __LINE__, TCM2_RC_VALUE);
        return 1;
    }
    
    u8 sign_data[1024] = {
        0x0c, 0x00, 0x03, 0x04, 0xed, 0x01, 0x00, 0x14, 0x8c, 0xb5, 0x14, 0x00, 0x0d, 0x01, 0x00, 0x14,
        0x8d, 0x11, 0x00, 0x5c, 0x0c, 0x00, 0xc0, 0x15, 0x0c, 0x00, 0x00, 0x16, 0x80, 0x01, 0x00, 0x4c,
        0x0c, 0x00, 0x28, 0x14, 0x8c, 0x01, 0x84, 0x03, 0x8d, 0x01, 0x80, 0x28, 0x0e, 0xf0, 0xbf, 0x02,
        0xad, 0xb9, 0x14, 0x00, 0x0e, 0x08, 0x80, 0x03, 0xad, 0x39, 0x15, 0x00, 0x8d, 0x01, 0x80, 0x29};
    u16 sign_data_len = 32;
    
    if(argc - arg_offset >= 4) {
        unsigned long long addr = strtoull(argv[2 + arg_offset], NULL, 0);
        sign_data_len = strtoul(argv[3 + arg_offset], NULL, 0);
        if(sign_data_len != 32) {
            tcm_err("%s:%d Input data size must be exactly 32 bytes for SM2 signature verification", __func__, __LINE__);
            return -1;
        }
        if(sign_data_len > sizeof(sign_data)) {
            tcm_err("%s:%d Input data too large, maximum size is %d bytes", __func__, __LINE__, sizeof(sign_data));
            return -1;
        }
        memcpy(sign_data, (void *)addr, sign_data_len);
    }
    
    tcm_print_data("[DEBUG] Sign data", sign_data, sign_data_len);

    TCMT_SIGNATURE signature;
    TCM2B_DIGEST digest;
    memcpy(digest.buffer, sign_data, sign_data_len);
    digest.size = sign_data_len;

    static TCMT_SIGNATURE last_signature;
    static int has_last_signature = 0;

    if (!verify_only) {
        result = tcm2_sign(sm2_key_handle, digest, &signature);
        if(result != TCM2_RC_SUCCESS) {
            tcm_err("%s:%d SM2 signing failed, error code: 0x%08X", __func__, __LINE__, result);
            return result;
        }
        
        memcpy(&last_signature, &signature, sizeof(TCMT_SIGNATURE));
        has_last_signature = 1;
        
        printf("SM2 Signing operation completed successfully.\n");
        printf("Signature R: ");
        for (int i = 0; i < signature.signature.sm2.signatureR.size; i++) {
            printf("%02x", signature.signature.sm2.signatureR.buffer[i]);
        }
        printf("\nSignature S: ");
        for (int i = 0; i < signature.signature.sm2.signatureS.size; i++) {
            printf("%02x", signature.signature.sm2.signatureS.buffer[i]);
        }
        printf("\n");
    }

    if (!sign_only) {
        TCMT_SIGNATURE *sig_to_verify;
        
        if (verify_only) {
            if (!has_last_signature) {
                tcm_err("%s:%d No previous signature available for verification", __func__, __LINE__);
                return TCM2_RC_FAILURE;
            }
            sig_to_verify = &last_signature;
        } else {
            sig_to_verify = &signature;
        }
        
        TCMT_TK_VERIFIED verification;
        result = tcm2_verify(sm2_key_handle, digest, sig_to_verify, &verification);
        if(result != TCM2_RC_SUCCESS) {
            tcm_err("%s:%d SM2 signature verification failed, error code: 0x%08X", __func__, __LINE__, result);
            return result;
        }

        printf("SM2 Signature verification succeeded.\n");
    }
    
    return TCM2_RC_SUCCESS;
}