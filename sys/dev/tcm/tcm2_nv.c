#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <tcm-v2.h>

#include "tcm-utils.h"
#include "tcm2_internal.h"
#include "tcm2_mailbox.h"

#define TCM_NV_SIZE 32  // Fixed size for NV space (32 bytes)
#define TCM_NV_ATTRIBUTES 0x00040004
#define TCM_NV_BASE_INDEX 0x1000000  // Base index for NV spaces
#define TCM_NV_MAX_BUFFER_SIZE 800

static u32 nv_indexarray[TCM_PT_NV_INDEX_MAX] = {0};
static u32 nv_index = 0x01000000;
static u32 nv_count = 0;

static TCM2_RC tcm2_nv_capbility() {
    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),   /* TAG */
        tcm_u32(22),                    /* Length */
        tcm_u32(TCM2_CC_GetCapability), /* Command code */

        tcm_u32(TCM_CAP_HANDLES),
        tcm_u32(0x01000000),
        tcm_u32(0x00000117)};
    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("Failed to flush context. Error code: 0x%08X\n", ret);
        return ret;
    }

    u32 offest = 17;
    nv_count = tcm_read_u16(response + offest) - 1;
    offest += 2;
    if(nv_count > 0) {
        u32 i;
        for(i = 0; i < nv_count; i++) {
            nv_indexarray[i] = tcm_read_u32(response + offest);
            offest += 4;
        }
        // printf("NV index: 0x%08X\n", nv_indexarray[i]);
    }
    return TCM2_RC_SUCCESS;
}

u32 tcm2_nv_define_space_impl(u32 index, u32 size) {
    if(size == 0 || size > TCM_NV_MAX_BUFFER_SIZE) {
        tcm_err("Invalid NV space size: %u (max: %u)\n", size, TCM_NV_MAX_BUFFER_SIZE);
        return 0;
    }

    TCM2_RC rc = tcm2_nv_capbility();
    if(rc != TCM2_RC_SUCCESS) {
        tcm_err("Failed to get NV capability. Error code: 0x%08X\n", rc);
        return 0;
    }
    u32 new_index = index ? index : TCM_NV_BASE_INDEX + nv_count;
    
    u32 tcm_rh = TCM2_RH_OWNER;
    u32 tcm_nv_attr = TCM_NV_ATTRIBUTES;
    
    if(new_index == 0x013ffffe || new_index == 0x013ffffd) {
        tcm_rh = TCM2_RH_PLATFORM;
        tcm_nv_attr = 0x40014001;
    } else if(new_index == 0x013fffff) {
        tcm_rh = TCM2_RH_PLATFORM;
        tcm_nv_attr = 0x42070001;
    }
    
    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),       /* TAG */
        tcm_u32(45),                     /* Length */
        tcm_u32(TCM2_CC_NV_DefineSpace), /* Command code */

        /* HANDLE */
        tcm_u32(tcm_rh), /* TCM resource handle */

        /* AUTH_SESSION */
        tcm_u32(9),          /* Authorization size */
        tcm_u32(TCM2_RS_PW), /* Session handle */
        tcm_u16(0),          /* nonce size */
        0,                   /* Session attributes */
        tcm_u16(0),          /* auth size */

        /* TCM2B_AUTH */
        tcm_u16(0), /* auth size */

        /* TCM2B_NV_PUBLIC */
        tcm_u16(4 + 2 + 4 + 2 + 2), /* size of TCM2B_NV_PUBLIC */
        tcm_u32(new_index),         /* nvIndex */
        tcm_u16(TCM2_ALG_SM3_256),  /* nameAlg (SM3) */
        tcm_u32(tcm_nv_attr),       /* attributes */
        tcm_u16(0),                 /* authPolicy size */
        tcm_u16(size),              /* dataSize */
    };

    rc = tcm_sendrecv_command(command, NULL, NULL);
    if(rc != 0) {
        tcm_err("Failed to define NV space. Error code: 0x%08X\n", rc);
        return 0;
    }
    nv_index = new_index;
    return nv_index;
}

TCM2_RC tcm2_nv_undefine_space_impl(u32 space_index) {

    u32 tcm_rh = TCM2_RH_OWNER;
    if(space_index == 0x013ffffe || space_index == 0x013ffffd || space_index == 0x013fffff)
        tcm_rh = TCM2_RH_PLATFORM;

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),         /* TAG */
        tcm_u32(31),                       /* Length */
        tcm_u32(TCM2_CC_NV_UndefineSpace), /* Command code */

        /* HANDLE */
        tcm_u32(tcm_rh), /* TCM resource handle */
        tcm_u32(space_index),   /* NV index */

        /* AUTH_SESSION */
        tcm_u32(9),          /* Authorization size */
        tcm_u32(TCM2_RS_PW), /* Session handle */
        tcm_u16(0),          /* nonce size */
        0,                   /* Session attributes */
        tcm_u16(0),          /* auth size */
    };

    return tcm_sendrecv_command(command, NULL, NULL);
}

u32 tcm2_nv_read_impl(u32 index, void *data, size_t data_len, size_t *new_size) {

    u32 tcm_rh = index;
    if(index == 0x013ffffe || index == 0x013ffffd || index == 0x013fffff)
        tcm_rh = TCM2_RH_PLATFORM;

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS), /* TAG */
        tcm_u32(35),               /* Length */
        tcm_u32(TCM2_CC_NV_Read),  /* Command code */

        /* HANDLE */
        tcm_u32(tcm_rh), /* TCM resource handle */
        tcm_u32(index), /* NV index */

        /* AUTH_SESSION */
        tcm_u32(9),          /* Authorization size */
        tcm_u32(TCM2_RS_PW), /* Session handle */
        tcm_u16(0),          /* nonce size */
        0,                   /* Session attributes */
        tcm_u16(0),          /* auth size */

        /* Parameters */
        tcm_u16(data_len), /* size */
        tcm_u16(0),        /* read size */
    };

    u8 response[COMMAND_BUFFER_SIZE];
    size_t response_length = sizeof(response);
    u32 rc = tcm_sendrecv_command(command, response, &response_length);
    if(rc) {
        tcm_err("Failed to read NV space. Error code: 0x%08X\n", rc);
        return rc;
    }

    /* Extract data from response */
    if(response_length > 10) {
        u16 resp_data_size = tcm_read_u16(&response[14]);
        size_t actual_size = resp_data_size;
        tcm_debug("resp_data_size: %d\n", resp_data_size);
        if(resp_data_size > 0 && resp_data_size <= data_len) {
            // Check for consecutive 0x00
            /* for(size_t i = 0; i < resp_data_size - 3; i++) {
                if(response[16+i] == 0x00 && response[16+i+1] == 0x00 && response[16+i+2] == 0x00 && response[16+i+3] == 0x00) {
                    actual_size = i;
                    break;
                }
            } */

            memcpy(data, &response[16], actual_size);
            *new_size = actual_size;
            return 0;
        }
    }

    return 0;
}

u32 tcm2_nv_write_impl(u32 index, const void *data, size_t data_len) {
    u32 tcm_rh = index;
    if(index == 0x013ffffe || index == 0x013ffffd || index == 0x013fffff)
        tcm_rh = TCM2_RH_PLATFORM;

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),  /* TAG */
        tcm_u32(33 + data_len + 2), /* Length */
        tcm_u32(TCM2_CC_NV_Write),  /* Command code */

        /* HANDLE */
        tcm_u32(tcm_rh), /* TCM resource handle */
        tcm_u32(index), /* NV index */

        /* AUTH_SESSION */
        tcm_u32(9),          /* Authorization size */
        tcm_u32(TCM2_RS_PW), /* Session handle */
        tcm_u16(0),          /* nonce size */
        0,                   /* Session attributes */
        tcm_u16(0),          /* auth size */

        /* Parameters */
        tcm_u16(data_len), /* data size */
    };

    /* Copy raw binary data to command buffer */
    memcpy(&command[33], data, data_len);
    tcm_write_u16(&command[33 + data_len], 0);

    return tcm_sendrecv_command(command, NULL, NULL);
}

u32 tcm2_nv_readpublic_impl(u32 index) {
    TCM2_RC rc = tcm2_nv_capbility();
    if(rc != TCM2_RC_SUCCESS) {
        tcm_err("Failed to get NV capability. Error code: 0x%08X\n", rc);
        return rc;
    }

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),   /* TAG */
        tcm_u32(14),                    /* Length */
        tcm_u32(TCM2_CC_NV_ReadPublic), /* Command code */
        tcm_u32(index),                 /* NV index */
    };

    u8 response[COMMAND_BUFFER_SIZE];
    size_t response_length = sizeof(response);
    rc = tcm_sendrecv_command(command, response, &response_length);
    if(rc) {
        // tcm_err("Failed to read NV public info. Error code: 0x%08X\n", rc);
        return rc;
    }

    u16 size = tcm_read_u16(&response[10]);
    printf("nv index 0x%08x:\n", index);

    /* printf("\tname: 0x");
    for(int i = 0; i < tcm_read_u16(&response[26]); i++) {
        printf("%02x", response[28 + i]);
    }
    printf("\n");
    printf("\thash algorithm:\n");
    if(tcm_read_u16(&response[16]) == TCM2_ALG_SM3_256) {
        printf("\t\tfriendly: sm3_256\n");
        printf("\t\tvalue: 0x%08x\n", TCM2_ALG_SM3_256);
    }
    printf("\tattributes:\n");
    printf("\t\tfriendly: authwrite|authread\n");
    printf("\t\tvalue: 0x40004\n");
    printf("\tsize: %u\n", tcm_read_u32(&response[22])); */

    return 0;
}

int tcm2_nvdefine_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: tcm2_nvdefine [index] [size]\n");
        printf("Define a new NV space with SM3 algorithm\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  [index]        : Optional - Specific index to use (e.g. 0x013ffffe, 0x013fffff, 0x013ffffd)\n");
        printf("  [size]         : Optional - Size of NV space in bytes, can be decimal or hex (e.g. 32 or 0x20)\n");
        printf("                   If not specified, size is determined by index:\n");
        printf("                   - 0x013ffffe: 0x20c (524 bytes)\n");
        printf("                   - 0x013fffff: 0x40 (64 bytes)\n");
        printf("                   - 0x013ffffd: 0x104 (260 bytes)\n");
        printf("                   - Others: 0x20 (32 bytes)\n");
        printf("                   (max: %u bytes)\n", TCM_NV_MAX_BUFFER_SIZE);
        printf("Examples:\n");
        printf("  tcm2_nvdefine             : Define NV space with default size (32 bytes) and auto-generated index\n");
        printf("  tcm2_nvdefine 64          : Define NV space with size 64 bytes and auto-generated index\n");
        printf("  tcm2_nvdefine 0x013ffffe  : Define NV space with size 0x20c (524 bytes) and index 0x013ffffe\n");
        printf("  tcm2_nvdefine 0x013fffff  : Define NV space with size 0x40 (64 bytes) and index 0x013fffff\n");
        printf("  tcm2_nvdefine 0x013ffffd  : Define NV space with size 0x104 (260 bytes) and index 0x013ffffd\n");
        printf("  tcm2_nvdefine 0x01000000 0x20 : Define NV space with size 0x20 (32 bytes) and index 0x01000000\n");
        return 0;
    }
    
    if(argc != 1 && argc != 2 && argc != 3) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    u32 index = 0;
    u32 size = TCM_NV_SIZE;

    if(argc == 2) {
        char *arg = argv[1];
        if(arg[0] == '0' && (arg[1] == 'x' || arg[1] == 'X')) {
            index = strtoul(arg, NULL, 0);
            if(index == 0x013ffffe) {
                size = 0x20c; /* 524 bytes */
            } else if(index == 0x013fffff) {
                size = 0x40;  /* 64 bytes */
            } else if(index == 0x013ffffd) {
                size = 0x104; /* 260 bytes */
            } else {
                size = TCM_NV_SIZE; /* default 32 bytes */
            }
        } else {
            size = strtoul(arg, NULL, 0);
        }
    } else if(argc == 3) {
        index = strtoul(argv[1], NULL, 0);
        size = strtoul(argv[2], NULL, 0);
    }

    u32 result_index = tcm2_nv_define_space_impl(index, size);
    if(result_index != 0) {
        printf("Allocated index: 0x%x\n", result_index);
        nv_index++;
        return 0;
    } else {
        tcm_err("Failed to define NV space\n");
        return -1;
    }
}

int tcm2_nvundefine_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: tcm2_nvundefine <index>\n");
        printf("Undefine an NV space\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <index>        : NV space index to undefine (e.g., 0x1000000)\n");
        printf("Examples:\n");
        printf("  tcm2_nvundefine 0x1000000  : Undefine NV space with index 0x1000000\n");
        return 0;
    }
    
    if(argc != 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    u32 index = strtoul(argv[1], NULL, 0);
    printf("Undefining NV space 0x%x\n", index);

    int ret = tcm2_nv_undefine_space_impl(index);
    if(ret == 0) {
        printf("NV space undefined successfully\n");
        if(nv_index > TCM_NV_BASE_INDEX)
            nv_index--;
    } else {
        tcm_err("The handle is not correct for the use, error code: %d\n", ret);
    }
    return ret;
}

static u32 tcm2_nv_read_size(u32 index) {
    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),   /* TAG */
        tcm_u32(14),                    /* Length */
        tcm_u32(TCM2_CC_NV_ReadPublic), /* Command code */
        tcm_u32(index),                 /* NV index */
    };
    u8 response[COMMAND_BUFFER_SIZE];
    size_t response_length = sizeof(response);
    int rc = tcm_sendrecv_command(command, response, &response_length);
    if(rc != 0) {
        printf("Failed to read NV public info, error code: %d\n", rc);
        return rc;
    }
    return tcm_read_u32(&response[22]);
}

int tcm2_nvread_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: tcm2_nvread <index>\n");
        printf("Read data from NV space\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <index>        : NV space index to read from (e.g., 0x1000000)\n");
        printf("Examples:\n");
        printf("  tcm2_nvread 0x1000000  : Read data from NV space with index 0x1000000\n");
        return 0;
    }
    
    if(argc != 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    u32 index = strtoul(argv[1], NULL, 0);
    u32 ssize = tcm2_nv_read_size(index);
    u8 data[ssize];
    u32 new_size = 0;
    int ret = tcm2_nv_read_impl(index, data, ssize, &new_size);
    if(ret == 0) {
        tcm_print_data("Hex data:", data, new_size);
        return 0;
    } else {
        tcm_err("Failed to read NV space, error code\n");
        return TCM2_RC_NV_SIZE;
    }
}

int tcm2_nvwrite_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: tcm2_nvwrite <index> <data> [size]\n");
        printf("       tcm2_nvwrite <index> <address> <size>\n");
        printf("Write data to NV space\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <index>        : NV space index to write to (e.g., 0x1000000)\n");
        printf("  <data>         : Data to write to the NV space, or a 64-bit memory address\n");
        printf("  [size]         : Optional - Size of data to write when <data> is a memory address\n");
        printf("Examples:\n");
        printf("  tcm2_nvwrite 0x1000000 123456  : Write '123456' to NV space with index 0x1000000\n");
        printf("  tcm2_nvwrite 0x013ffffe 0x900000000b023d20 32 : Write 32 bytes from memory address 0x900000000b023d20\n");
        return 0;
    }
    
    if(argc != 3 && argc != 4) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    u32 index = strtoul(argv[1], NULL, 0);
    u32 nv_size = tcm2_nv_read_size(index);
    u8 buffer[TCM_NV_MAX_BUFFER_SIZE] = {0};
    size_t data_len = 0;
    
    if(argc == 4) {
        unsigned long long addr = strtoull(argv[2], NULL, 0);
        data_len = strtoul(argv[3], NULL, 0);
        
        if(data_len > nv_size) {
            printf("Error: Data too long (NV space size: %u bytes)\n", nv_size);
            return -1;
        }
        
        if(data_len > TCM_NV_MAX_BUFFER_SIZE) {
            printf("Error: Data too long (Max buffer size: %u bytes)\n", TCM_NV_MAX_BUFFER_SIZE);
            return -1;
        }
        
        memcpy(buffer, (void *)addr, data_len);
    } else {
        const char *input = argv[2];
        data_len = strlen(input);
        
        if(data_len > nv_size) {
            printf("Error: Data too long (NV space size: %u bytes)\n", nv_size);
            return -1;
        }
        
        memcpy(buffer, input, data_len);
    }

    // tcm_print_data("nv write data", buffer, data_len);

    int ret = tcm2_nv_write_impl(index, buffer, data_len);
    if(ret == 0) {
        printf("Data written successfully\n");
    } else {
        printf("Failed to write NV space, error code: 0x%08x \n", ret);
    }
    return ret;
}

int tcm2_nvreadpublic_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);
    
    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: tcm2_nvreadpublic [index]\n");
        printf("Read public information of NV space\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  [index]        : Optional - NV space index to read public info from (e.g., 0x1000000)\n");
        printf("                   If not provided, read all NV spaces\n");
        printf("Examples:\n");
        printf("  tcm2_nvreadpublic           : Read public info from all NV spaces\n");
        printf("  tcm2_nvreadpublic 0x1000000 : Read public info from NV space with index 0x1000000\n");
        return 0;
    }
    
    if(argc > 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(argc == 1) {
        TCM2_RC rc = tcm2_nv_capbility();
        if(rc != TCM2_RC_SUCCESS) {
            tcm_err("Failed to get NV capability. Error code: 0x%08X\n", rc);
            return rc;
        }

        if(nv_count == 0) {
            printf("No NV spaces found\n");
            return 0;
        }

        for(u32 i = 0; i < nv_count; i++) {
            int ret = tcm2_nv_readpublic_impl(nv_indexarray[i]);
            if(ret != 0) {
                tcm_err("Failed to read NV public info for index 0x%x, error code: %d\n",
                        nv_indexarray[i], ret);
            }
        }
        return 0;
    } else {
        u32 index = strtoul(argv[1], NULL, 0);
        int ret = tcm2_nv_readpublic_impl(index);
        if(ret != 0) {
            tcm_err("Failed to read NV public info, error code: %d\n", ret);
        }
        return ret;
    }
}
