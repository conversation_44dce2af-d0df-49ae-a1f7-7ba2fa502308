#ifndef TCM_MAILBOX_H__
#define TCM_MAILBOX_H__

#include <stdio.h>
#include <string.h>
#include <tcm-common.h>

extern int debug_data;

/* Check if there is a -c option in command line arguments
 * If yes, set debug_data = 1 to enable debug output
 * If no, set debug_data = 0
 * Return value: number of arguments after processing (after removing -c option)
 */
int tcm2_check_debug_flag(int *argc, char ***argv);

/********************** INT **********************/
#define SE_INT_ALL	        0xff
#define SE_INI_CLEAN	     (0U << 0)
#define SE_INT_CMD	        (1U << 0)
#define SE_INT_ASYN	        (2U << 0)

#define ACK_SUCCESS				0x0
#define ACK_ERROR		0x60
#define ACK_LEN_ERR		0x67
#define ACK_NOPROBE		0x6a
#define ACK_ILLEGAL_INS		0x6d
#define ACK_ILLEGAL_CLA		0x6e


void tcm_write_u8(u8 *buffer, u8 value);
void tcm_write_u16(u8 *buffer, u16 value);
void tcm_write_u32(u8 *buffer, u32 value);
u32 tcm_read_u32(const u8 *buffer);
u16 tcm_read_u16(const u8 *buffer);

void *parse_byte_string(char *bytes, u8 *data, u32 *count_ptr);
int pack_byte_string(u8 *str, u32 size, const char *format, ...);
int unpack_byte_string(const u8 *str, size_t size, const char *format, ...);
void hex_to_bytes(const char *hex_str, u8 *byte_array, u32 *byte_len);
#endif
