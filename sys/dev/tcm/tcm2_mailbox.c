#include <sys/linux/types.h>
#include <stdarg.h>

#include "tcm2_mailbox.h"

#define DIV_ROUND_UP(n, d) (((n) + (d) - 1) / (d))


void tcm_write_u8(u8 *buffer, u8 value)
{
    buffer[0] = value & 0xFF;
}

void tcm_write_u16(u8 *buffer, u16 value)
{
	buffer[0] = (value >> 8) & 0xFF;
	buffer[1] = value & 0xFF;
}

void tcm_write_u32(u8 *buffer, u32 value)
{
	buffer[0] = (value >> 24) & 0xFF;
	buffer[1] = (value >> 16) & 0xFF;
	buffer[2] = (value >> 8) & 0xFF;
	buffer[3] = value & 0xFF;
}

// Read 32-bit unsigned integer from byte array (big endian)
u32 tcm_read_u32(const u8 *buffer) {
    return ((u32)buffer[0] << 24) |
           ((u32)buffer[1] << 16) |
           ((u32)buffer[2] << 8)  |
           ((u32)buffer[3]);
}

// Read 16-bit unsigned integer from byte array (big endian)
u16 tcm_read_u16(const u8 *buffer) {
    return ((u16)buffer[0] << 8) |
           ((u16)buffer[1]);
}

void hex_to_bytes(const char *hex_str, u8 *byte_array, u32 *byte_len) {
    size_t hex_len = strlen(hex_str);
    *byte_len = hex_len / 2;
    for (size_t i = 0; i < *byte_len; i++) {
        sscanf(&hex_str[i * 2], "%2hhx", &byte_array[i]);
    }
}

void tcm2_print_pcr_data(int index, const u8 *buffer, u32 len)
{
    printf("%3d: 0x", index);
    for(int j = 0; j < len; j++) {
        printf("%02x", buffer[j]);
    }
    printf("\n");
}