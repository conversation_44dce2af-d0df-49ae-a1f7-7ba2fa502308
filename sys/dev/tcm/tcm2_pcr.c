#include <TcmTypes.h>
#include <ctype.h>
#include <errno.h>
#include <machine/va-loongarch.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/arch/loongarch/include/stdarg.h>
#include <sys/types.h>
#include <tcm-common.h>
#include <tcm-v2.h>

#include "tcm-utils.h"
#include "tcm2_internal.h"
#include "tcm2_log.h"
#include "tcm2_mailbox.h"
#include "tcm2_tis.h"

#define DIV_ROUND_UP(n, d) (((n) + (d) - 1) / (d))

u32 tcm2_pcr_extend(u32 index, u32 algorithm, const u8 *digest, u32 digest_len) {
    uint offset = 33;
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(offset + digest_len),
        tcm_u32(TCM2_CC_PCR_Extend),
        tcm_u32(index),
        tcm_u32(9),
        tcm_u32(TCM2_RS_PW),
        tcm_u16(0),
        0,
        tcm_u16(0),
        tcm_u32(1),
        tcm_u16(algorithm)};

    int res = pack_byte_string(command_v2, COMMAND_BUFFER_SIZE, "s",
                               offset, digest, digest_len);
    u32 response_len = COMMAND_BUFFER_SIZE;
    u8 response[COMMAND_BUFFER_SIZE];

    int ret = tcm_sendrecv_command(command_v2, response, &response_len);
    if(ret) {
        tcm_err("Error in tcm_sendrecv_command: %d\n", ret);
        return ret;
    }
    u32 counter = 0;
    int unpack_res = unpack_byte_string(response, response_len, "ds", 10, &counter, response_len - digest_len, digest, digest_len);
    if(unpack_res) {
        tcm_err("unpack_byte_string failed with result: %d\n", unpack_res);
        return TCM_LIB_ERROR;
    }
    return 0;
}

u32 tcm2_pcr_read(TCMS_PCR_SELECT *pcr_select,
                  u16 algorithm, TCML_DIGEST *digest_list,
                  u32 *updates) {
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(17 + pcr_select->sizeofSelect),
        tcm_u32(TCM2_CC_PCR_Read),
        tcm_u32(1),
        tcm_u16(algorithm),
        pcr_select->sizeofSelect};

    int res = pack_byte_string(command_v2, COMMAND_BUFFER_SIZE, "s",
                               17, pcr_select->pcrSelect, pcr_select->sizeofSelect);

    u32 response_len = COMMAND_BUFFER_SIZE;
    u8 response[COMMAND_BUFFER_SIZE];

    int ret = tcm_sendrecv_command(command_v2, response, &response_len);
    if(ret) {
        tcm_err("Error in tcm_sendrecv_command: %d\n", ret);
        return ret;
    }

    // Parse response
    // Offset 10: counter
    // Offset 24: TCML_DIGEST.count
    // Followed by TCML_DIGEST.digests array
    u32 counter = 0;

    // Read counter
    int unpack_res = unpack_byte_string(response, response_len, "d",
                                        10, &counter);
    if(unpack_res) {
        tcm_err("Failed to unpack counter: %d\n", unpack_res);
        return TCM_LIB_ERROR;
    }

    // Read TCML_DIGEST.count
    unpack_res = unpack_byte_string(response, response_len, "d",
                                    24, &digest_list->count);
    if(unpack_res) {
        tcm_err("Failed to unpack digest count: %d\n", unpack_res);
        return TCM_LIB_ERROR;
    }

    if(digest_list->count > 8) {
        tcm_err("Too many digests: %d\n", digest_list->count);
        return TCM_LIB_ERROR;
    }

    // Read each digest value
    u8 *src = response + 28;  // Point to the first digest value
    for(int i = 0; i < digest_list->count; i++) {
        u16 size = (src[0] << 8) | src[1];  // Read size field (should be 0x0020)
        if(size != TCM2_DIGEST_LEN) {
            tcm_err("Unexpected digest size: %d\n", size);
            return TCM_LIB_ERROR;
        }
        src += 2;  // Skip size field
        memcpy(digest_list->digests[i].buffer, src, TCM2_DIGEST_LEN);
        src += TCM2_DIGEST_LEN;
    }

    if(updates)
        *updates = counter;

    return 0;
}

u32 tcm2_pcr_reset(u32 index) {
    u8 command_v2[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),  /* TAG */
        tcm_u32(27),                /* length */
        tcm_u32(TCM2_CC_PCR_Reset), /* command code */

        /* HANDLE */
        tcm_u32(index), /* PCR index */

        /* AUTH_SESSION */
        tcm_u32(9),          /* authorization size */
        tcm_u32(TCM2_RS_PW), /* session handle */
        tcm_u16(0),          /* <nonce> size */
        0,                   /* attributes: Cont/Excl/Rst */
        tcm_u16(0)           /* <hmac/password> size */
    };

    return tcm_sendrecv_command(command_v2, NULL, NULL);
}

int tcm2_pcrreset_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);

    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <pcr>\n", argv[0]);
        printf("Reset PCR to its default value\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <pcr>          : Index of the PCR to reset (0-23)\n");
        printf("Examples:\n");
        printf("  %s 0           : Reset PCR #0 to its default value\n", argv[0]);
        printf("  %s 16          : Reset PCR #16 to its default value\n", argv[0]);
        return 0;
    }

    if(argc != 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    u32 index = strtoul(argv[1], NULL, 0);

    if(index >= 24) {
        tcm_err("do_tcm_pcr_reset: Invalid PCR index\n");
        return -EINVAL;
    }

    int ret = tcm2_pcr_reset(index);
    if(ret) {
        tcm_err("Failed to reset PCR #%u, error code: %d\n", index, ret);
    }
    return ret;
}

int tcm2_pcrread_cmd(int argc, char **argv) {
    u32 updates;
    int ret;
    TCML_DIGEST digest_list[3];  // Store results from three reads
    TCMS_PCR_SELECT pcr_select = {0};
    int i, j;
    char *token;
    char str_copy[256];
    u32 pcr_count = 0;

    tcm2_check_debug_flag(&argc, &argv);

    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s [pcr_indices]\n", argv[0]);
        printf("Read PCR values from the TCM\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  [pcr_indices]  : Optional - Comma-separated list of PCR indices to read (e.g., 0,1,2)\n");
        printf("                   If not provided, all PCRs (0-23) will be read\n");
        printf("Examples:\n");
        printf("  %s             : Read all PCRs (0-23)\n", argv[0]);
        printf("  %s 0,1,2,3     : Read PCRs 0, 1, 2, and 3\n", argv[0]);
        printf("  %s 16,17,18    : Read PCRs 16, 17, and 18\n", argv[0]);
        printf("use: tcm2_pcr_read [pcr1,pcr2,...]\n");
        printf("  [pcr1,pcr2,...]: comma-separated list of PCR indices (0-23, max 8)\n");
        return 0;
    }
    if(argc == 1) {
        // First time: read PCR 0-7
        pcr_select.sizeofSelect = 3;
        pcr_select.pcrSelect[0] = 0xff;
        ret = tcm2_pcr_read(&pcr_select, TCM2_ALG_SM3_256, &digest_list[0], &updates);
        if(ret < 0) return ret;

        // Second time: read PCR 8-15
        memset(pcr_select.pcrSelect, 0, sizeof(pcr_select.pcrSelect));
        pcr_select.pcrSelect[1] = 0xff;
        ret = tcm2_pcr_read(&pcr_select, TCM2_ALG_SM3_256, &digest_list[1], &updates);
        if(ret < 0) return ret;

        // Third time: read PCR 16-23
        memset(pcr_select.pcrSelect, 0, sizeof(pcr_select.pcrSelect));
        pcr_select.pcrSelect[2] = 0xff;
        ret = tcm2_pcr_read(&pcr_select, TCM2_ALG_SM3_256, &digest_list[2], &updates);
        if(ret < 0) return ret;

        // Print all PCR values uniformly
        printf("sm3_256:\n");
        // Print PCR 0-7
        for(i = 0; i < digest_list[0].count; i++) {
            tcm2_print_pcr_data(i, digest_list[0].digests[i].buffer, TCM2_DIGEST_LEN);
        }
        // Print PCR 8-15
        for(i = 0; i < digest_list[1].count; i++) {
            tcm2_print_pcr_data(i + 8, digest_list[1].digests[i].buffer, TCM2_DIGEST_LEN);
        }
        // Print PCR 16-23
        for(i = 0; i < digest_list[2].count; i++) {
            tcm2_print_pcr_data(i + 16, digest_list[2].digests[i].buffer, TCM2_DIGEST_LEN);
        }
        return 0;
    } else if(argc == 2) {
        if(strlen(argv[1]) >= sizeof(str_copy)) {
            tcm_err("Input string too long\n");
            return -EINVAL;
        }
        strcpy(str_copy, argv[1]);

        pcr_select.sizeofSelect = 3;
        token = strtok(str_copy, ",");
        while(token && pcr_count < 8) {
            u32 index = strtoul(token, NULL, 0);
            if(index >= 24) {
                tcm_err("PCR index %u must be between 0 and 23\n", index);
                return -EINVAL;
            }
            pcr_select.pcrSelect[index / 8] |= BIT(index % 8);
            pcr_count++;
            token = strtok(NULL, ",");
            if(token && pcr_count >= 8) {
                tcm_err("Maximum 8 PCRs can be read at once, ignoring remaining indices\n");
                break;
            }
        }

        ret = tcm2_pcr_read(&pcr_select, TCM2_ALG_SM3_256, &digest_list[0], &updates);
        if(ret < 0) return ret;

        printf("sm3_256:\n");
        int digest_index = 0;
        for(i = 0; i < 24; i++) {
            if(pcr_select.pcrSelect[i / 8] & BIT(i % 8)) {
                tcm2_print_pcr_data(i, digest_list[0].digests[digest_index].buffer, TCM2_DIGEST_LEN);
                digest_index++;
            }
        }
        return 0;
    }

    return -1;
}

int tcm2_pcrextend_cmd(int argc, char **argv) {
    u32 index;
    int ret;
    char *colon_pos, *equals_pos;
    char *alg_str;
    u8 digest[TCM2_DIGEST_LEN];
    int i;
    char hex_str[3];

    tcm2_check_debug_flag(&argc, &argv);

    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <pcr>:sm3_256=<32-byte-hex>\n", argv[0]);
        printf("Extend a PCR with the specified digest value\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <pcr>          : Index of the PCR to extend (0-23)\n");
        printf("  sm3_256        : Hash algorithm to use (only sm3_256 is supported)\n");
        printf("  <32-byte-hex>  : 32-byte digest value in hexadecimal format (64 hex characters)\n");
        printf("Examples:\n");
        printf("  %s 0:sm3_256=1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef\n", argv[0]);
        printf("  %s 16:sm3_256=0000000000000000000000000000000000000000000000000000000000000000\n", argv[0]);
        return 0;
    }

    if(argc != 2) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    colon_pos = strchr(argv[1], ':');
    if(!colon_pos) {
        tcm_err("Invalid format. Expected <pcr>:sm3_256=<value>\n");
        return -EINVAL;
    }

    *colon_pos = '\0';
    index = strtoul(argv[1], NULL, 0);
    if(index >= 24) {
        tcm_err("PCR index must be between 0 and 23\n");
        return -EINVAL;
    }

    alg_str = colon_pos + 1;
    equals_pos = strchr(alg_str, '=');
    if(!equals_pos) {
        tcm_err("Invalid format. Expected <pcr>:sm3_256=<value>\n");
        return -EINVAL;
    }

    *equals_pos = '\0';
    if(strcmp(alg_str, "sm3_256") != 0) {
        tcm_err("Only sm3_256 algorithm is supported\n");
        return -EINVAL;
    }

    char *hex_value = equals_pos + 1;
    if(strlen(hex_value) != TCM2_DIGEST_LEN * 2) {
        tcm_err("Digest must be exactly 32 bytes (64 hex characters)\n");
        return -EINVAL;
    }

    hex_str[2] = '\0';
    for(i = 0; i < TCM2_DIGEST_LEN; i++) {
        hex_str[0] = hex_value[i * 2];
        hex_str[1] = hex_value[i * 2 + 1];
        if(!isxdigit(hex_str[0]) || !isxdigit(hex_str[1])) {
            tcm_err("Invalid hex characters in digest\n");
            return -EINVAL;
        }
        digest[i] = (u8)strtoul(hex_str, NULL, 16);
    }

    ret = tcm2_pcr_extend(index, TCM2_ALG_SM3_256, digest, TCM2_DIGEST_LEN);
    if(ret) {
        tcm_err("Failed to extend PCR #%u, error code: %d\n", index, ret);
    }
    return ret;
}
