/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Defines APIs and structures that allow software to interact with a
 * TCM2 device
 *
 * Copyright (c) 2020 Linaro
 * Copyright (c) 2018 Bootlin
 *
 * https://trustedcomputinggroup.org/resource/tss-overview-common-structures-specification/
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 */

#ifndef __TCM_V2_H
#define __TCM_V2_H

#include <tcm-common.h>
#include <TcmBaseTypes.h>

typedef unsigned char	unchar;		/* Sys V compatibility */
typedef	unsigned short	ushort;		/* Sys V compatibility */
typedef	unsigned int	uint;		/* Sys V compatibility */
typedef unsigned long	ulong;		/* Sys V compatibility */

#define __packed __attribute__((packed, aligned(1)))

#define TCM2_DIGEST_LEN		32

#define TCM2_SHA1_DIGEST_SIZE 20
#define TCM2_SHA256_DIGEST_SIZE	32
#define TCM2_SHA384_DIGEST_SIZE	48
#define TCM2_SHA512_DIGEST_SIZE	64
#define TCM2_SM3_256_DIGEST_SIZE 32

#define TCM2_MAX_PCRS 32
#define TCM2_PCR_SELECT_MAX ((TCM2_MAX_PCRS + 7) / 8)
#define TCM2_MAX_CAP_BUFFER 1024
#define TCM2_MAX_TCM_PROPERTIES ((TCM2_MAX_CAP_BUFFER - sizeof(u32) /* TCM2_CAP */ - \
				 sizeof(u32)) / sizeof(struct tcms_tagged_property))

#define TCM2_HDR_LEN		10
#define TCM2_MAX_DATASIZE	1024

/*
 *  We deviate from this draft of the specification by increasing the value of
 *  TCM2_NUM_PCR_BANKS from 3 to 16 to ensure compatibility with TCM2
 *  implementations that have enabled a larger than typical number of PCR
 *  banks. This larger value for TCM2_NUM_PCR_BANKS is expected to be included
 *  in a future revision of the specification.
 */
#define TCM2_NUM_PCR_BANKS 16

/* Definition of (UINT32) TCM2_CAP Constants */
#define TCM2_CAP_PCRS 0x00000005U
#define TCM2_CAP_TCM_PROPERTIES 0x00000006U

/* Definition of (UINT32) TCM2_PT Constants */
#define TCM2_PT_GROUP			(u32)(0x00000100)
#define TCM2_PT_FIXED			(u32)(TCM2_PT_GROUP * 1)
#define TCM2_PT_MANUFACTURER		(u32)(TCM2_PT_FIXED + 5)
#define TCM2_PT_PCR_COUNT		(u32)(TCM2_PT_FIXED + 18)
#define TCM2_PT_MAX_COMMAND_SIZE	(u32)(TCM2_PT_FIXED + 30)
#define TCM2_PT_MAX_RESPONSE_SIZE	(u32)(TCM2_PT_FIXED + 31)

/*
 * event types, cf.
 * "TCG Server Management Domain Firmware Profile Specification",
 * rev 1.00, 2020-05-01
 */
#define EV_POST_CODE			((u32)0x00000001)
#define EV_NO_ACTION			((u32)0x00000003)
#define EV_SEPARATOR			((u32)0x00000004)
#define EV_ACTION			((u32)0x00000005)
#define EV_TAG				((u32)0x00000006)
#define EV_S_CRTM_CONTENTS		((u32)0x00000007)
#define EV_S_CRTM_VERSION		((u32)0x00000008)
#define EV_CPU_MICROCODE		((u32)0x00000009)
#define EV_PLATFORM_CONFIG_FLAGS	((u32)0x0000000A)
#define EV_TABLE_OF_DEVICES		((u32)0x0000000B)
#define EV_COMPACT_HASH			((u32)0x0000000C)

/*
 * event types, cf.
 * "TCG PC Client Platform Firmware Profile Specification", Family "2.0"
 * Level 00 Version 1.05 Revision 23, May 7, 2021
 */
#define EV_EFI_EVENT_BASE			((u32)0x80000000)
#define EV_EFI_VARIABLE_DRIVER_CONFIG		((u32)0x80000001)
#define EV_EFI_VARIABLE_BOOT			((u32)0x80000002)
#define EV_EFI_BOOT_SERVICES_APPLICATION	((u32)0x80000003)
#define EV_EFI_BOOT_SERVICES_DRIVER		((u32)0x80000004)
#define EV_EFI_RUNTIME_SERVICES_DRIVER		((u32)0x80000005)
#define EV_EFI_GPT_EVENT			((u32)0x80000006)
#define EV_EFI_ACTION				((u32)0x80000007)
#define EV_EFI_PLATFORM_FIRMWARE_BLOB		((u32)0x80000008)
#define EV_EFI_HANDOFF_TABLES			((u32)0x80000009)
#define EV_EFI_PLATFORM_FIRMWARE_BLOB2		((u32)0x8000000A)
#define EV_EFI_HANDOFF_TABLES2			((u32)0x8000000B)
#define EV_EFI_VARIABLE_BOOT2			((u32)0x8000000C)
#define EV_EFI_HCRTM_EVENT			((u32)0x80000010)
#define EV_EFI_VARIABLE_AUTHORITY		((u32)0x800000E0)
#define EV_EFI_SPDM_FIRMWARE_BLOB		((u32)0x800000E1)
#define EV_EFI_SPDM_FIRMWARE_CONFIG		((u32)0x800000E2)

#define EFI_CALLING_EFI_APPLICATION         \
	"Calling EFI Application from Boot Option"
#define EFI_RETURNING_FROM_EFI_APPLICATION  \
	"Returning from EFI Application from Boot Option"
#define EFI_EXIT_BOOT_SERVICES_INVOCATION   \
	"Exit Boot Services Invocation"
#define EFI_EXIT_BOOT_SERVICES_FAILED       \
	"Exit Boot Services Returned with Failure"
#define EFI_EXIT_BOOT_SERVICES_SUCCEEDED    \
	"Exit Boot Services Returned with Success"

/* TCMS_TAGGED_PROPERTY Structure */
struct tcms_tagged_property {
	u32 property;
	u32 value;
} __packed;

/* TCMS_PCR_SELECTION Structure */
struct tcms_pcr_selection {
	u16 hash;
	u8 size_of_select;
	u8 pcr_select[TCM2_PCR_SELECT_MAX];
} __packed;

/* TCML_PCR_SELECTION Structure */
struct tcml_pcr_selection {
	u32 count;
	struct tcms_pcr_selection selection[TCM2_NUM_PCR_BANKS];
} __packed;

/* TCML_TAGGED_TCM_PROPERTY Structure */
struct tcml_tagged_tcm_property {
	u32 count;
	struct tcms_tagged_property tcm_property[TCM2_MAX_TCM_PROPERTIES];
} __packed;

/* TCMU_CAPABILITIES Union */
union tcmu_capabilities {
	/*
	 * Non exhaustive. Only added the structs needed for our
	 * current code
	 */
	struct tcml_pcr_selection assigned_pcr;
	struct tcml_tagged_tcm_property tcm_properties;
} __packed;

/* TCMS_CAPABILITY_DATA Structure */
struct tcms_capability_data {
	u32 capability;
	union tcmu_capabilities data;
} __packed;

/**
 * SHA1 Event Log Entry Format
 *
 * @pcr_index:	PCRIndex event extended to
 * @event_type:	Type of event (see EFI specs)
 * @digest:	Value extended into PCR index
 * @event_size:	Size of event
 * @event:	Event data
 */
struct tcg_pcr_event {
	u32 pcr_index;
	u32 event_type;
	u8 digest[TCM2_SHA1_DIGEST_SIZE];
	u32 event_size;
	u8 event[];
} __packed;

/**
 * Definition of TCMU_HA Union
 */
union tmpu_ha {
	u8 sha1[TCM2_SHA1_DIGEST_SIZE];
	u8 sha256[TCM2_SHA256_DIGEST_SIZE];
	u8 sm3_256[TCM2_SM3_256_DIGEST_SIZE];
	u8 sha384[TCM2_SHA384_DIGEST_SIZE];
	u8 sha512[TCM2_SHA512_DIGEST_SIZE];
} __packed;

/**
 * Definition of TCMT_HA Structure
 *
 * @hash_alg:	Hash algorithm defined in enum tcm2_algorithms
 * @digest:	Digest value for a given algorithm
 */
struct tcmt_ha {
	u16 hash_alg;
	union tmpu_ha digest;
} __packed;

/**
 * Definition of TCML_DIGEST_VALUES Structure
 *
 * @count:	Number of algorithms supported by hardware
 * @digests:	struct for algorithm id and hash value
 */
struct tcml_digest_values {
	u32 count;
	struct tcmt_ha digests[TCM2_NUM_PCR_BANKS];
} __packed;

/**
 * Crypto Agile Log Entry Format
 *
 * @pcr_index:	PCRIndex event extended to
 * @event_type:	Type of event
 * @digests:	List of digestsextended to PCR index
 * @event_size: Size of the event data
 * @event:	Event data
 */
struct tcg_pcr_event2 {
	u32 pcr_index;
	u32 event_type;
	struct tcml_digest_values digests;
	u32 event_size;
	u8 event[];
} __packed;

/**
 * TCM2 Structure Tags for command/response buffers.
 *
 * @TCM2_ST_NO_SESSIONS: the command does not need an authentication.
 * @TCM2_ST_SESSIONS: the command needs an authentication.
 */
enum tcm2_structures {
	TCM2_ST_NO_SESSIONS	= 0x8001,
	TCM2_ST_SESSIONS	= 0x8002,
};

/**
 * TCM2 type of boolean.
 */
enum tcm2_yes_no {
	TCMI_YES		= 1,
	TCMI_NO			= 0,
};

/**
 * TCM2 startup values.
 *
 * @TCM2_SU_CLEAR: reset the internal state.
 * @TCM2_SU_STATE: restore saved state (if any).
 */
enum tcm2_startup_types {
	TCM2_SU_CLEAR		= 0x0000,
	TCM2_SU_STATE		= 0x0001,
};

/**
 * TCM2 permanent handles.
 *
 * @TCM2_RH_OWNER: refers to the 'owner' hierarchy.
 * @TCM2_RS_PW: indicates a password.
 * @TCM2_RH_LOCKOUT: refers to the 'lockout' hierarchy.
 * @TCM2_RH_ENDORSEMENT: refers to the 'endorsement' hierarchy.
 * @TCM2_RH_PLATFORM: refers to the 'platform' hierarchy.
 */
/* Definition of TCM2_HANDLE TCM2_RH Constants <S>*/
/*Represents different resource and permission handles in TCM (Trusted Platform Module),
used to reference and manage these resources in different TCM operations and commands.
Each constant corresponds to a specific TCM resource or permission with different functions and purposes.*/
typedef UINT32 TCM2_HANDLE;

typedef TCM2_HANDLE TCM2_RH;
#define TCM2_RH_FIRST       ((TCM2_RH) 0x40000000) /* R */
#define TCM2_RH_SRK         ((TCM2_RH) 0x40000000) /* R */
#define TCM2_RH_OWNER       ((TCM2_RH) 0x40000001) /* K A P */
#define TCM2_RH_REVOKE      ((TCM2_RH) 0x40000002) /* R */
#define TCM2_RH_TRANSPORT   ((TCM2_RH) 0x40000003) /* R */
#define TCM2_RH_OPERATOR    ((TCM2_RH) 0x40000004) /* R */
#define TCM2_RH_ADMIN       ((TCM2_RH) 0x40000005) /* R */
#define TCM2_RH_EK          ((TCM2_RH) 0x40000006) /* R */
#define TCM2_RH_NULL        ((TCM2_RH) 0x40000007) /* K A P */
#define TCM2_RH_UNASSIGNED  ((TCM2_RH) 0x40000008) /* R */
#define TCM2_RH_PW          ((TCM2_RH) 0x40000009) /* S */
#define TCM2_RS_PW          ((TCM2_RH) 0x40000009) /* S; This was a bug; to be deprecated*/
#define TCM2_RH_LOCKOUT     ((TCM2_RH) 0x4000000A) /* A */
#define TCM2_RH_ENDORSEMENT ((TCM2_RH) 0x4000000B) /* K A P */
#define TCM2_RH_PLATFORM    ((TCM2_RH) 0x4000000C) /* K A P */
#define TCM2_RH_PLATFORM_NV ((TCM2_RH) 0x4000000D) /* C */
#define TCM2_RH_AUTH_00     ((TCM2_RH) 0x40000010) /* A */
#define TCM2_RH_AUTH_FF     ((TCM2_RH) 0x4000010F) /* A */
#define TCM2_RH_ACT_0       ((TCM2_RH) 0x40000110) /* A P */
#define TCM2_RH_ACT_1       ((TCM2_RH) 0x40000111)
#define TCM2_RH_ACT_2       ((TCM2_RH) 0x40000112)
#define TCM2_RH_ACT_3       ((TCM2_RH) 0x40000113)
#define TCM2_RH_ACT_4       ((TCM2_RH) 0x40000114)
#define TCM2_RH_ACT_5       ((TCM2_RH) 0x40000115)
#define TCM2_RH_ACT_6       ((TCM2_RH) 0x40000116)
#define TCM2_RH_ACT_7       ((TCM2_RH) 0x40000117)
#define TCM2_RH_ACT_8       ((TCM2_RH) 0x40000118)
#define TCM2_RH_ACT_9       ((TCM2_RH) 0x40000119)
#define TCM2_RH_ACT_A       ((TCM2_RH) 0x4000011A)
#define TCM2_RH_ACT_B       ((TCM2_RH) 0x4000011B)
#define TCM2_RH_ACT_C       ((TCM2_RH) 0x4000011C)
#define TCM2_RH_ACT_D       ((TCM2_RH) 0x4000011D)
#define TCM2_RH_ACT_E       ((TCM2_RH) 0x4000011E)
#define TCM2_RH_ACT_F       ((TCM2_RH) 0x4000011F) /* A P */
#define TCM2_RH_FW_OWNER             ((TCM2_RH) 0x40000140) /* K */
#define TCM2_RH_FW_ENDORSEMENT       ((TCM2_RH) 0x40000141) /* K */
#define TCM2_RH_FW_PLATFORM          ((TCM2_RH) 0x40000142) /* K */
#define TCM2_RH_FW_NULL              ((TCM2_RH) 0x40000143) /* K */
#define TCM2_RH_SVN_OWNER_BASE       ((TCM2_RH) 0x40010000) /* K */
#define TCM2_RH_SVN_ENDORSEMENT_BASE ((TCM2_RH) 0x40020000) /* K */
#define TCM2_RH_SVN_PLATFORM_BASE    ((TCM2_RH) 0x40030000) /* K */
#define TCM2_RH_SVN_NULL_BASE        ((TCM2_RH) 0x40040000) /* K */
#define TCM2_RH_LAST                 ((TCM2_RH) 0x4004FFFF) /* R */

/* Definition of UINT32 TCMA_OBJECT Bits */
typedef uint32_t TCMA_OBJECT;

#define TCMA_OBJECT_RESERVED1_MASK       ((TCMA_OBJECT) 0x00000001) /* Should be zero */
#define TCMA_OBJECT_FIXEDTCM             ((TCMA_OBJECT) 0x00000002) /* Set to 1: The object's hierarchy as indicated by its qualified name is unchangeable. Clear to 0: The object's hierarchy may change because the object or an ancestor key is duplicated to another hierarchy. */
#define TCMA_OBJECT_STCLEAR              ((TCMA_OBJECT) 0x00000004) /* Set to 1: The previously saved context of this object shall not be loaded after StartupCLEAR. Clear to 0: The saved context of this object may be used after ShutdownSTATE and subsequent Startup. */
#define TCMA_OBJECT_RESERVED2_MASK       ((TCMA_OBJECT) 0x00000008) /* Should be zero */
#define TCMA_OBJECT_FIXEDPARENT          ((TCMA_OBJECT) 0x00000010) /* Set to 1: The object's parent is unchangeable. Clear to 0: The object's parent may change due to TCM2_Duplicate object. */
#define TCMA_OBJECT_SENSITIVEDATAORIGIN  ((TCMA_OBJECT) 0x00000020) /* Set to 1: Indicates that when the object was created by TCM2_Create or TCM2_CreatePrimary, the TCM generated all sensitive data other than authValue. Clear to 0: Some sensitive data other than authValue was provided by the caller. */
#define TCMA_OBJECT_USERWITHAUTH         ((TCMA_OBJECT) 0x00000040) /* Set to 1: User role operations may be approved by HMAC session or password using this object's authValue, or approved through policy session. Clear to 0: User role operations may only be approved through policy session. */
#define TCMA_OBJECT_ADMINWITHPOLICY      ((TCMA_OBJECT) 0x00000080) /* Set to 1: Admin role operations may only be approved through policy session. Clear to 0: Admin role operations may be approved by HMAC session, password using object authValue, or policy session. */
#define TCMA_OBJECT_FIRMWARELIMITED      ((TCMA_OBJECT) 0x00000100) /* Set to 1: Object is in firmware-limited hierarchy */
#define TCMA_OBJECT_SVNLIMITED           ((TCMA_OBJECT) 0x00000200) /* Set to 1: Object is in SVN-limited hierarchy */
#define TCMA_OBJECT_RESERVED3_MASK       ((TCMA_OBJECT) 0x00000300) /* Should be zero */
#define TCMA_OBJECT_NODA                 ((TCMA_OBJECT) 0x00000400) /* Set to 1: The object is not subject to dictionary attack protection. Clear to 0: The object is subject to dictionary attack protection. */
#define TCMA_OBJECT_ENCRYPTEDDUPLICATION ((TCMA_OBJECT) 0x00000800) /* Set to 1: If the object is duplicated, symmetricAlg shall not be TCM2_ALG_NULL and newParentHandle shall not be TCM2_RH_NULL. Clear to 0: The object may duplicate its private portion without inner wrapping, and the new parent may be TCM2_RH_NULL. */
#define TCMA_OBJECT_RESERVED4_MASK       ((TCMA_OBJECT) 0x0000F000) /* Should be zero */
#define TCMA_OBJECT_RESTRICTED           ((TCMA_OBJECT) 0x00010000) /* Set to 1: Key usage is restricted to operations on structures of known format, and the parent of this key must be set to restricted. Clear to 0: Key usage is not restricted to special formats. */
#define TCMA_OBJECT_DECRYPT              ((TCMA_OBJECT) 0x00020000) /* Set to 1: The private portion of the key may be used for decryption. Clear to 0: The private portion of the key may not be used for decryption. */
#define TCMA_OBJECT_SIGN_ENCRYPT         ((TCMA_OBJECT) 0x00040000) /* Set to 1: For symmetric encryption objects, the private portion of the key may be used for encryption; for other objects, the private portion of the key may be used for signing. Clear to 0: The private portion of the key may not be used for signing or encryption. */
#define TCMA_OBJECT_X509SIGN             ((TCMA_OBJECT) 0x00080000) /* Set to 1: For asymmetric keys, the private portion of the key may not be used as a signing key in TCM2_Sign. Clear to 0: The key may be used as a signing key in TCM2_Sign. */
#define TCMA_OBJECT_RESERVED5_MASK       ((TCMA_OBJECT) 0xFFF80000) /* Should be zero */


/**
 * TCM2 command codes used at the beginning of a buffer, gives the command.
 *
 * @TCM2_CC_STARTUP: TCM2_Startup().
 * @TCM2_CC_SELF_TEST: TCM2_SelfTest().
 * @TCM2_CC_CLEAR: TCM2_Clear().
 * @TCM2_CC_CLEARCONTROL: TCM2_ClearControl().
 * @TCM2_CC_HIERCHANGEAUTH: TCM2_HierarchyChangeAuth().
 * @TCM2_CC_DAM_RESET: TCM2_DictionaryAttackLockReset().
 * @TCM2_CC_DAM_PARAMETERS: TCM2_DictionaryAttackParameters().
 * @TCM2_CC_GET_CAPABILITY: TCM2_GetCapibility().
 * @TCM2_CC_GET_RANDOM: TCM2_GetRandom().
 * @TCM2_CC_PCR_READ: TCM2_PCR_Read().
 * @TCM2_CC_PCR_EXTEND: TCM2_PCR_Extend().
 * @TCM2_CC_PCR_SETAUTHVAL: TCM2_PCR_SetAuthValue().
 */
/* TCM 2.0 Part 2: Table 2:12 - Definition of TCM_CC Constants */
typedef UINT32                              TCM_CC;
#define TYPE_OF_TCM_CC                      UINT32
#define TCM2_CC_NV_UndefineSpaceSpecial      (TCM_CC)(0x0000011F)
#define TCM2_CC_EvictControl                 (TCM_CC)(0x00000120)
#define TCM2_CC_HierarchyControl             (TCM_CC)(0x00000121)
#define TCM2_CC_NV_UndefineSpace             (TCM_CC)(0x00000122)
#define TCM2_CC_ChangeEPS                    (TCM_CC)(0x00000124)
#define TCM2_CC_ChangePPS                    (TCM_CC)(0x00000125)
#define TCM2_CC_Clear                        (TCM_CC)(0x00000126)
#define TCM2_CC_ClearControl                 (TCM_CC)(0x00000127)
#define TCM2_CC_ClockSet                     (TCM_CC)(0x00000128)
#define TCM2_CC_HierarchyChangeAuth          (TCM_CC)(0x00000129)
#define TCM2_CC_NV_DefineSpace               (TCM_CC)(0x0000012A)
#define TCM2_CC_PCR_Allocate                 (TCM_CC)(0x0000012B)
#define TCM2_CC_PCR_SetAuthPolicy            (TCM_CC)(0x0000012C)
#define TCM2_CC_PP_Commands                  (TCM_CC)(0x0000012D)
#define TCM2_CC_SetPrimaryPolicy             (TCM_CC)(0x0000012E)
#define TCM2_CC_FieldUpgradeStart            (TCM_CC)(0x0000012F)
#define TCM2_CC_ClockRateAdjust              (TCM_CC)(0x00000130)
#define TCM2_CC_CreatePrimary                (TCM_CC)(0x00000131)
#define TCM2_CC_NV_GlobalWriteLock           (TCM_CC)(0x00000132)
#define TCM2_CC_GetCommandAuditDigest        (TCM_CC)(0x00000133)
#define TCM2_CC_NV_Increment                 (TCM_CC)(0x00000134)
#define TCM2_CC_NV_SetBits                   (TCM_CC)(0x00000135)
#define TCM2_CC_NV_Extend                    (TCM_CC)(0x00000136)
#define TCM2_CC_NV_Write                     (TCM_CC)(0x00000137)
#define TCM2_CC_NV_WriteLock                 (TCM_CC)(0x00000138)
#define TCM2_CC_DictionaryAttackLockReset    (TCM_CC)(0x00000139)
#define TCM2_CC_DictionaryAttackParameters   (TCM_CC)(0x0000013A)
#define TCM2_CC_NV_ChangeAuth                (TCM_CC)(0x0000013B)
#define TCM2_CC_PCR_Event                    (TCM_CC)(0x0000013C)
#define TCM2_CC_PCR_Reset                    (TCM_CC)(0x0000013D)
#define TCM2_CC_SequenceComplete             (TCM_CC)(0x0000013E)
#define TCM2_CC_SetAlgorithmSet              (TCM_CC)(0x0000013F)
#define TCM2_CC_SetCommandCodeAuditStatus    (TCM_CC)(0x00000140)
#define TCM2_CC_FieldUpgradeData             (TCM_CC)(0x00000141)
#define TCM2_CC_IncrementalSelfTest          (TCM_CC)(0x00000142)
#define TCM2_CC_SelfTest                     (TCM_CC)(0x00000143)
#define TCM2_CC_Startup                      (TCM_CC)(0x00000144)
#define TCM2_CC_Shutdown                     (TCM_CC)(0x00000145)
#define TCM2_CC_StirRandom                   (TCM_CC)(0x00000146)
#define TCM2_CC_ActivateCredential           (TCM_CC)(0x00000147)
#define TCM2_CC_Certify                      (TCM_CC)(0x00000148)
#define TCM2_CC_PolicyNV                     (TCM_CC)(0x00000149)
#define TCM2_CC_CertifyCreation              (TCM_CC)(0x0000014A)
#define TCM2_CC_Duplicate                    (TCM_CC)(0x0000014B)
#define TCM2_CC_GetTime                      (TCM_CC)(0x0000014C)
#define TCM2_CC_GetSessionAuditDigest        (TCM_CC)(0x0000014D)
#define TCM2_CC_NV_Read                      (TCM_CC)(0x0000014E)
#define TCM2_CC_NV_ReadLock                  (TCM_CC)(0x0000014F)
#define TCM2_CC_ObjectChangeAuth             (TCM_CC)(0x00000150)
#define TCM2_CC_PolicySecret                 (TCM_CC)(0x00000151)
#define TCM2_CC_Rewrap                       (TCM_CC)(0x00000152)
#define TCM2_CC_Create                       (TCM_CC)(0x00000153)
#define TCM2_CC_ECDH_ZGen                    (TCM_CC)(0x00000154)
#define TCM2_CC_HMAC                         (TCM_CC)(0x00000155)
#define TCM2_CC_MAC                          (TCM_CC)(0x00000155)
#define TCM2_CC_Import                       (TCM_CC)(0x00000156)
#define TCM2_CC_Load                         (TCM_CC)(0x00000157)
#define TCM2_CC_Quote                        (TCM_CC)(0x00000158)
#define TCM2_CC_RSA_Decrypt                  (TCM_CC)(0x00000159)
#define TCM2_CC_HMAC_Start                   (TCM_CC)(0x0000015B)
#define TCM2_CC_MAC_Start                    (TCM_CC)(0x0000015B)
#define TCM2_CC_SequenceUpdate               (TCM_CC)(0x0000015C)
#define TCM2_CC_Sign                         (TCM_CC)(0x0000015D)
#define TCM2_CC_Unseal                       (TCM_CC)(0x0000015E)
#define TCM2_CC_PolicySigned                 (TCM_CC)(0x00000160)
#define TCM2_CC_ContextLoad                  (TCM_CC)(0x00000161)
#define TCM2_CC_ContextSave                  (TCM_CC)(0x00000162)
#define TCM2_CC_ECDH_KeyGen                  (TCM_CC)(0x00000163)
#define TCM2_CC_EncryptDecrypt               (TCM_CC)(0x00000164)
#define TCM2_CC_FlushContext                 (TCM_CC)(0x00000165)
#define TCM2_CC_LoadExternal                 (TCM_CC)(0x00000167)
#define TCM2_CC_MakeCredential               (TCM_CC)(0x00000168)
#define TCM2_CC_NV_ReadPublic                (TCM_CC)(0x00000169)
#define TCM2_CC_PolicyAuthorize              (TCM_CC)(0x0000016A)
#define TCM2_CC_PolicyAuthValue              (TCM_CC)(0x0000016B)
#define TCM2_CC_PolicyCommandCode            (TCM_CC)(0x0000016C)
#define TCM2_CC_PolicyCounterTimer           (TCM_CC)(0x0000016D)
#define TCM2_CC_PolicyCpHash                 (TCM_CC)(0x0000016E)
#define TCM2_CC_PolicyLocality               (TCM_CC)(0x0000016F)
#define TCM2_CC_PolicyNameHash               (TCM_CC)(0x00000170)
#define TCM2_CC_PolicyOR                     (TCM_CC)(0x00000171)
#define TCM2_CC_PolicyTicket                 (TCM_CC)(0x00000172)
#define TCM2_CC_ReadPublic                   (TCM_CC)(0x00000173)
#define TCM2_CC_RSA_Encrypt                  (TCM_CC)(0x00000174)
#define TCM2_CC_StartAuthSession             (TCM_CC)(0x00000176)
#define TCM2_CC_VerifySignature              (TCM_CC)(0x00000177)
#define TCM2_CC_ECC_Parameters               (TCM_CC)(0x00000178)
#define TCM2_CC_FirmwareRead                 (TCM_CC)(0x00000179)
#define TCM2_CC_GetCapability                (TCM_CC)(0x0000017A)
#define TCM2_CC_GetRandom                    (TCM_CC)(0x0000017B)
#define TCM2_CC_GetTestResult                (TCM_CC)(0x0000017C)
#define TCM2_CC_Hash                         (TCM_CC)(0x0000017D)
#define TCM2_CC_PCR_Read                     (TCM_CC)(0x0000017E)
#define TCM2_CC_PolicyPCR                    (TCM_CC)(0x0000017F)
#define TCM2_CC_PolicyRestart                (TCM_CC)(0x00000180)
#define TCM2_CC_ReadClock                    (TCM_CC)(0x00000181)
#define TCM2_CC_PCR_Extend                   (TCM_CC)(0x00000182)
#define TCM2_CC_PCR_SetAuthValue             (TCM_CC)(0x00000183)
#define TCM2_CC_NV_Certify                   (TCM_CC)(0x00000184)
#define TCM2_CC_EventSequenceComplete        (TCM_CC)(0x00000185)
#define TCM2_CC_HashSequenceStart            (TCM_CC)(0x00000186)
#define TCM2_CC_PolicyPhysicalPresence       (TCM_CC)(0x00000187)
#define TCM2_CC_PolicyDuplicationSelect      (TCM_CC)(0x00000188)
#define TCM2_CC_PolicyGetDigest              (TCM_CC)(0x00000189)
#define TCM2_CC_TestParms                    (TCM_CC)(0x0000018A)
#define TCM2_CC_Commit                       (TCM_CC)(0x0000018B)
#define TCM2_CC_PolicyPassword               (TCM_CC)(0x0000018C)
#define TCM2_CC_ZGen_2Phase                  (TCM_CC)(0x0000018D)
#define TCM2_CC_EC_Ephemeral                 (TCM_CC)(0x0000018E)
#define TCM2_CC_PolicyNvWritten              (TCM_CC)(0x0000018F)
#define TCM2_CC_PolicyTemplate               (TCM_CC)(0x00000190)
#define TCM2_CC_CreateLoaded                 (TCM_CC)(0x00000191)
#define TCM2_CC_PolicyAuthorizeNV            (TCM_CC)(0x00000192)
#define TCM2_CC_EncryptDecrypt2              (TCM_CC)(0x00000193)
#define TCM2_CC_AC_GetCapability             (TCM_CC)(0x00000194)
#define TCM2_CC_AC_Send                      (TCM_CC)(0x00000195)
#define TCM2_CC_Policy_AC_SendSelect         (TCM_CC)(0x00000196)
#define TCM2_CC_CertifyX509                  (TCM_CC)(0x00000197)
#define TCM2_CC_ACT_SetTimeout               (TCM_CC)(0x00000198)
#define TCM2_CC_ECC_Encrypt		    (TCM_CC)(0x00000199)
#define TCM2_CC_ECC_Decrypt		    (TCM_CC)(0x0000019A)
#define CC_VEND                             0x20000000
#define TCM2_CC_Vendor_TCG_Test              (TCM_CC)(0x20000000)

#define NTC2_CC_PreConfig		    (TCM_CC)(0x20000211)
#define NTC2_CC_LockPreConfig               (TCM_CC)(0x20000212)
#define NTC2_CC_GetConfig                   (TCM_CC)(0x20000213)

/* TCG Algorithm Registry: Table 1:2 - Definition of TCM2_ALG_ID Constants */

typedef UINT16 TCM2_ALG_ID;

#define TCM2_ALG_ERROR               ((TCM2_ALG_ID) 0x0000)
#define TCM2_ALG_RSA                 ((TCM2_ALG_ID) 0x0001)
#define TCM2_ALG_TDES                ((TCM2_ALG_ID) 0x0003)
#define TCM2_ALG_SHA                 ((TCM2_ALG_ID) 0x0004)
#define TCM2_ALG_SHA1                ((TCM2_ALG_ID) 0x0004)
#define TCM2_ALG_HMAC                ((TCM2_ALG_ID) 0x0005)
#define TCM2_ALG_AES                 ((TCM2_ALG_ID) 0x0006)
#define TCM2_ALG_MGF1                ((TCM2_ALG_ID) 0x0007)
#define TCM2_ALG_KEYEDHASH           ((TCM2_ALG_ID) 0x0008)
#define TCM2_ALG_XOR                 ((TCM2_ALG_ID) 0x000A)
#define TCM2_ALG_SHA256              ((TCM2_ALG_ID) 0x000B)
#define TCM2_ALG_SHA384              ((TCM2_ALG_ID) 0x000C)
#define TCM2_ALG_SHA512              ((TCM2_ALG_ID) 0x000D)
#define TCM2_ALG_NULL                ((TCM2_ALG_ID) 0x0010)
#define TCM2_ALG_SM3_256             ((TCM2_ALG_ID) 0x0012)
#define TCM2_ALG_SM4                 ((TCM2_ALG_ID) 0x0013)
#define TCM2_ALG_RSASSA              ((TCM2_ALG_ID) 0x0014)
#define TCM2_ALG_RSAES               ((TCM2_ALG_ID) 0x0015)
#define TCM2_ALG_RSAPSS              ((TCM2_ALG_ID) 0x0016)
#define TCM2_ALG_OAEP                ((TCM2_ALG_ID) 0x0017)
#define TCM2_ALG_ECDSA               ((TCM2_ALG_ID) 0x0018)
#define TCM2_ALG_ECDH                ((TCM2_ALG_ID) 0x0019)
#define TCM2_ALG_ECDAA               ((TCM2_ALG_ID) 0x001A)
#define TCM2_ALG_SM2                 ((TCM2_ALG_ID) 0x001B)
#define TCM2_ALG_ECSCHNORR           ((TCM2_ALG_ID) 0x001C)
#define TCM2_ALG_ECMQV               ((TCM2_ALG_ID) 0x001D)
#define TCM2_ALG_KDF1_SP800_56A      ((TCM2_ALG_ID) 0x0020)
#define TCM2_ALG_KDF2                ((TCM2_ALG_ID) 0x0021)
#define TCM2_ALG_KDF1_SP800_108      ((TCM2_ALG_ID) 0x0022)
#define TCM2_ALG_ECC                 ((TCM2_ALG_ID) 0x0023)
#define TCM2_ALG_SYMCIPHER           ((TCM2_ALG_ID) 0x0025)
#define TCM2_ALG_CAMELLIA            ((TCM2_ALG_ID) 0x0026)
#define TCM2_ALG_CMAC                ((TCM2_ALG_ID) 0x003F)
#define TCM2_ALG_CTR                 ((TCM2_ALG_ID) 0x0040)
#define TCM2_ALG_SHA3_256            ((TCM2_ALG_ID) 0x0027)
#define TCM2_ALG_SHA3_384            ((TCM2_ALG_ID) 0x0028)
#define TCM2_ALG_SHA3_512            ((TCM2_ALG_ID) 0x0029)
#define TCM2_ALG_OFB                 ((TCM2_ALG_ID) 0x0041)
#define TCM2_ALG_CBC                 ((TCM2_ALG_ID) 0x0042)
#define TCM2_ALG_CFB                 ((TCM2_ALG_ID) 0x0043)
#define TCM2_ALG_ECB                 ((TCM2_ALG_ID) 0x0044)
#define TCM2_ALG_FIRST               ((TCM2_ALG_ID) 0x0001)
#define TCM2_ALG_LAST                ((TCM2_ALG_ID) 0x0044)

/* From TCG Algorithm Registry: Definition of TCM2_ECC_CURVE Constants */

typedef UINT16                TCM2_ECC_CURVE;
#define TCM2_ECC_NONE         ((TCM2_ECC_CURVE) 0x0000)
#define TCM2_ECC_NIST_P192    ((TCM2_ECC_CURVE) 0x0001)
#define TCM2_ECC_NIST_P224    ((TCM2_ECC_CURVE) 0x0002)
#define TCM2_ECC_NIST_P256    ((TCM2_ECC_CURVE) 0x0003)
#define TCM2_ECC_NIST_P384    ((TCM2_ECC_CURVE) 0x0004)
#define TCM2_ECC_NIST_P521    ((TCM2_ECC_CURVE) 0x0005)
#define TCM2_ECC_BN_P256      ((TCM2_ECC_CURVE) 0x0010)
#define TCM2_ECC_BN_P638      ((TCM2_ECC_CURVE) 0x0011)
#define TCM2_ECC_SM2_P256     ((TCM2_ECC_CURVE) 0x0020)

/* NV index attributes */
/* enum tcm_index_attrs {
	TCMA_NV_PPWRITE		= 1UL << 0,
	TCMA_NV_OWNERWRITE	= 1UL << 1,
	TCMA_NV_AUTHWRITE	= 1UL << 2,
	TCMA_NV_POLICYWRITE	= 1UL << 3,
	TCMA_NV_COUNTER		= 1UL << 4,
	TCMA_NV_BITS		= 1UL << 5,
	TCMA_NV_EXTEND		= 1UL << 6,
	TCMA_NV_POLICY_DELETE	= 1UL << 10,
	TCMA_NV_WRITELOCKED	= 1UL << 11,
	TCMA_NV_WRITEALL	= 1UL << 12,
	TCMA_NV_WRITEDEFINE	= 1UL << 13,
	TCMA_NV_WRITE_STCLEAR	= 1UL << 14,
	TCMA_NV_GLOBALLOCK	= 1UL << 15,
	TCMA_NV_PPREAD		= 1UL << 16,
	TCMA_NV_OWNERREAD	= 1UL << 17,
	TCMA_NV_AUTHREAD	= 1UL << 18,
	TCMA_NV_POLICYREAD	= 1UL << 19,
	TCMA_NV_NO_DA		= 1UL << 25,
	TCMA_NV_ORDERLY		= 1UL << 26,
	TCMA_NV_CLEAR_STCLEAR	= 1UL << 27,
	TCMA_NV_READLOCKED	= 1UL << 28,
	TCMA_NV_WRITTEN		= 1UL << 29,
	TCMA_NV_PLATFORMCREATE	= 1UL << 30,
	TCMA_NV_READ_STCLEAR	= 1UL << 31,

	TCMA_NV_MASK_READ	= TCMA_NV_PPREAD | TCMA_NV_OWNERREAD |
				TCMA_NV_AUTHREAD | TCMA_NV_POLICYREAD,
	TCMA_NV_MASK_WRITE	= TCMA_NV_PPWRITE | TCMA_NV_OWNERWRITE |
					TCMA_NV_AUTHWRITE | TCMA_NV_POLICYWRITE,
}; */

enum {
	TCM_ACCESS_VALID		= 1 << 7,
	TCM_ACCESS_ACTIVE_LOCALITY	= 1 << 5,
	TCM_ACCESS_REQUEST_PENDING	= 1 << 2,
	TCM_ACCESS_REQUEST_USE		= 1 << 1,
	TCM_ACCESS_ESTABLISHMENT	= 1 << 0,
};

enum {
	TCM_STS_FAMILY_SHIFT		= 26,
	TCM_STS_FAMILY_MASK		= 0x3 << TCM_STS_FAMILY_SHIFT,
	TCM_STS_FAMILY_TCM2		= 1 << TCM_STS_FAMILY_SHIFT,
	TCM_STS_RESE_TESTABLISMENT_BIT	= 1 << 25,
	TCM_STS_COMMAND_CANCEL		= 1 << 24,
	TCM_STS_BURST_COUNT_SHIFT	= 8,
	TCM_STS_BURST_COUNT_MASK	= 0xffff << TCM_STS_BURST_COUNT_SHIFT,
	TCM_STS_VALID			= 1 << 7,
	TCM_STS_COMMAND_READY		= 1 << 6,
	TCM_STS_GO			= 1 << 5,
	TCM_STS_DATA_AVAIL		= 1 << 4,
	TCM_STS_DATA_EXPECT		= 1 << 3,
	TCM_STS_SELF_TEST_DONE		= 1 << 2,
	TCM_STS_RESPONSE_RETRY		= 1 << 1,
	TCM_STS_READ_ZERO               = 0x23
};

enum {
	TCM_CMD_COUNT_OFFSET	= 2,
	TCM_CMD_ORDINAL_OFFSET	= 6,
	TCM_MAX_BUF_SIZE	= 1260,
};

/* enum {
	TCM_HT_PCR = 0,
	TCM_HT_NV_INDEX,
	TCM_HT_HMAC_SESSION,
	TCM_HT_POLICY_SESSION,

	HR_SHIFT		= 24,
	HR_PCR			= TCM_HT_PCR << HR_SHIFT,
	HR_HMAC_SESSION		= TCM_HT_HMAC_SESSION << HR_SHIFT,
	HR_POLICY_SESSION	= TCM_HT_POLICY_SESSION << HR_SHIFT,
	HR_NV_INDEX		= TCM_HT_NV_INDEX << HR_SHIFT,
}; */

/**
 * Issue a TCM2_Startup command.
 *
 * @mode	TCM startup mode
 *
 * Return: code of the operation
 */
u32 tcm2_startup(enum tcm2_startup_types mode);

/**
 * Issue a TCM2_SelfTest command.
 *
 * @full_test	Asking to perform all tests or only the untested ones
 *
 * Return: code of the operation
 */
u32 tcm2_self_test(enum tcm2_yes_no full_test);

/**
 * Issue a TCM2_Clear command.
 *
 * @handle	Handle
 * @pw		Password
 * @pw_sz	Length of the password
 *
 * Return: code of the operation
 */
u32 tcm2_clear(u32 handle, const char *pw,
	       const ssize_t pw_sz);


u32 tcm2_nv_define_space(void);

/**
 * Issue a TCM2_PCR_Extend command.
 *
 * @dev		TCM device
 * @index	Index of the PCR
 * @algorithm	Algorithm used, defined in 'enum tcm2_algorithms'
 * @digest	Value representing the event to be recorded
 * @digest_len  len of the hash
 *
 * Return: code of the operation
 */
u32 tcm2_pcr_extend(u32 index, u32 algorithm,
		    const u8 *digest, u32 digest_len);

/**
 * Read data from the secure storage
 *
 * @index	Index of data to read
 * @data	Place to put data
 * @count	Number of bytes of data
 * Return: code of the operation
 */
u32 tcm2_nv_read_value(u32 index, void *data, u32 count);

/**
 * Write data to the secure storage
 *
 * @index	Index of data to write
 * @data	Data to write
 * @count	Number of bytes of data
 * Return: code of the operation
 */
u32 tcm2_nv_write_value(u32 index, const void *data,
			u32 count);


/**
 * Issue a TCM2_GetCapability command.  This implementation is limited
 * to query property index that is 4-byte wide.
 *
 * @dev		TCM device
 * @capability	Partition of capabilities
 * @property	Further definition of capability, limited to be 4 bytes wide
 * @buf		Output buffer for capability information
 * @prop_count	Size of output buffer
 *
 * Return: code of the operation
 */
u32 tcm2_get_capability(u32 capability, u32 property,
			void *buf, size_t prop_count);

/**
 * Issue a TCM2_DictionaryAttackLockReset command.
 *
 * @pw		Password
 * @pw_sz	Length of the password
 *
 * Return: code of the operation
 */
u32 tcm2_dam_reset(const char *pw, const ssize_t pw_sz);

/**
 * Issue a TCM2_DictionaryAttackParameters command.
 *
 * @pw		Password
 * @pw_sz	Length of the password
 * @max_tries	Count of authorizations before lockout
 * @recovery_time Time before decrementation of the failure count
 * @lockout_recovery Time to wait after a lockout
 *
 * Return: code of the operation
 */
u32 tcm2_dam_parameters(const char *pw,
			const ssize_t pw_sz, unsigned int max_tries,
			unsigned int recovery_time,
			unsigned int lockout_recovery);

/**
 * Issue a TCM2_HierarchyChangeAuth command.
 *
 * @handle	Handle
 * @newpw	New password
 * @newpw_sz	Length of the new password
 * @oldpw	Old password
 * @oldpw_sz	Length of the old password
 *
 * Return: code of the operation
 */
int tcm2_change_auth(u32 handle, const char *newpw,
		     const ssize_t newpw_sz, const char *oldpw,
		     const ssize_t oldpw_sz);


/**
 * Issue a TCM_PCR_SetAuthValue command.
 *
 * @pw		Platform password
 * @pw_sz	Length of the password
 * @index	Index of the PCR
 * @digest	New key to access the PCR
 * @key_sz	Length of the new key
 *
 * Return: code of the operation
 */
u32 tcm2_pcr_setauthvalue(const char *pw,
			  const ssize_t pw_sz, u32 index, const char *key,
			  const ssize_t key_sz);

/**
 * Issue a TCM2_GetRandom command.
 *
 * @param data		output buffer for the random bytes
 * @param count		size of output buffer
 *
 * Return: return code of the operation
 */
int tcm2_get_random(void *data, u32 count);

/**
 * Lock data in the TCM
 *
 * Once locked the data cannot be written until after a reboot
 *
 * @index	Index of data to lock
 * Return: code of the operation
 */
u32 tcm2_write_lock(u32 index);

/**
 * Disable access to any platform data
 *
 * This can be called to close off access to the firmware data in the data,
 * before calling the kernel.
 *
 * Return: code of the operation
 */
u32 tcm2_disable_platform_hierarchy();

/**
 * submit user specified data to the TCM and get response
 *
 * @sendbuf:	Buffer of the data to send
 * @recvbuf:	Buffer to save the response to
 * @recv_size:	Pointer to the size of the response buffer
 *
 * Return: code of the operation
 */
u32 tcm2_submit_command(const u8 *sendbuf,
			u8 *recvbuf, size_t *recv_size);

/**
 * tcm_cr50_report_state() - Report the Cr50 internal state
 *
 * @vendor_cmd:	Vendor command number to send
 * @vendor_subcmd: Vendor sub-command number to send
 * @recvbuf:	Buffer to save the response to
 * @recv_size:	Pointer to the size of the response buffer
 * Return: result of the operation
 */
u32 tcm2_report_state(uint vendor_cmd, uint vendor_subcmd,
		      u8 *recvbuf, size_t *recv_size);

/**
 * tcm2_enable_nvcommits() - Tell TCM to commit NV data immediately
 *
 * For Chromium OS verified boot, we may reboot or reset at different times,
 * possibly leaving non-volatile data unwritten by the TCM.
 *
 * This vendor command is used to indicate that non-volatile data should be
 * written to its store immediately.
 *
 * @vendor_cmd:	Vendor command number to send
 * @vendor_subcmd: Vendor sub-command number to send
 * Return: result of the operation
 */
u32 tcm2_enable_nvcommits(uint vendor_cmd,
			  uint vendor_subcmd);

#endif /* __TCM_V2_H */
